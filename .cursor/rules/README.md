# Cursor 规则文档

本项目包含以下Cursor规则，帮助AI助手更好地理解和维护POPUnion WordPress政府主题：

## 始终应用的规则 (alwaysApply: true)

### 1. chinese-development.mdc
**中文WordPress开发规范和本地化指南**
- 语言和本地化要求
- 中文字符处理
- 政府网站特殊要求
- 用户体验和SEO优化
- 编码规范和安全考虑

### 2. wordpress-theme-structure.mdc  
**POPUnion WordPress主题项目结构和组织规则**
- 主要文件和目录说明
- 资源目录组织
- 特殊页面模板介绍
- 主题特色功能概述

## 按需获取的规则 (description)

### 3. admin-check-system.mdc
**行政检查系统功能开发和维护指南**
- 核心文件结构
- 功能需求实现
- 数据结构设计
- AJAX处理和样式要求

### 4. functions-php-guidelines.mdc
**functions.php文件编辑指南和最佳实践**
- 文件结构理解
- 编辑安全规范
- 常见修改区域
- 代码质量要求和测试标准

### 5. page-template-conventions.mdc
**WordPress页面模板文件命名和结构约定**
- 模板文件命名规范
- 模板结构要求
- CSS/JS资源管理
- 响应式设计和SEO优化
- 无障碍访问标准

## 文件类型特定规则 (globs)

### 6. php-coding-standards.mdc
**适用于：*.php**
- 基础编码标准
- 文件编码和格式
- 命名约定
- 安全编码实践
- 错误处理和性能优化

### 7. css-js-management.mdc  
**适用于：*.css, *.js, *.scss**
- 文件结构和组织
- 资源加载策略
- CSS/JavaScript编码规范
- 性能优化
- 兼容性要求

## 使用说明

AI助手在处理不同类型的任务时，会自动应用相应的规则：

- **编辑PHP文件时**：自动应用php-coding-standards.mdc规则
- **处理CSS/JS文件时**：自动应用css-js-management.mdc规则  
- **需要了解行政检查系统时**：可以通过fetch_rules工具获取admin-check-system.mdc
- **修改functions.php时**：可以获取functions-php-guidelines.mdc指南
- **创建页面模板时**：可以参考page-template-conventions.mdc约定

所有规则都遵循中文开发规范，确保代码质量和政府网站标准。
