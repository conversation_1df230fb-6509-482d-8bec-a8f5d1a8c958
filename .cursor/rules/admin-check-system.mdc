---
description: 行政检查系统功能开发和维护指南
---

# 行政检查系统开发指南

## 核心文件结构
- [page-admin-check-nav.php](mdc:page-admin-check-nav.php) - 行政检查导航页面，包含分类和主体选择
- [page-admin-check-list.php](mdc:page-admin-check-list.php) - 行政检查列表页面，显示检查项目
- [archive-admin_check.php](mdc:archive-admin_check.php) - 行政检查归档页面
- [single-admin_check.php](mdc:single-admin_check.php) - 行政检查单页详情模板
- [taxonomy-check_category.php](mdc:taxonomy-check_category.php) - 检查类别分类页面

## 功能需求实现
基于 [DEMAND.md](mdc:DEMAND.md) 的要求：

### 1. 分类筛选功能
- 点击分类后，行政检查主体列表动态更新
- 下方项目列表显示该分类下的所有项目

### 2. 乡镇筛选功能  
- 点击乡镇时，如未选择分类则默认显示第一个分类
- 显示该乡镇的检查项目内容

### 3. 政府部门筛选功能
- 点击政府部门时，如未选择分类则默认显示第一个分类
- 显示该部门的检查项目内容

### 4. 详情页面
- 新增内容详情页面模板，风格与检查分类页面保持一致
- 点击项目标题后跳转到详情页面

## 数据结构
- 自定义文章类型：`admin_check`
- 自定义分类法：`check_category`
- 元字段：检查主体、乡镇、政府部门等

## AJAX处理
所有动态筛选功能需要在 [functions.php](mdc:functions.php) 中添加相应的AJAX处理函数，确保：
- 数据验证和安全过滤
- 返回JSON格式数据
- 错误处理机制

## 样式要求
- 保持与现有政府网站风格一致
- 响应式设计，支持移动端访问
- 符合政府网站无障碍访问标准