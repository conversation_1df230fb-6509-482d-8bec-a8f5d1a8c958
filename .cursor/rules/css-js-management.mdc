---
globs: *.css,*.js,*.scss
---

# CSS/JS资源管理规范

## 文件结构和组织
```
css/
├── gongkai1.css - gongkai6.css  # 政府公开页面专用样式
├── swiper.min.css               # 轮播组件样式
└── 其他主题样式文件

js/
├── jquery.min.js                # jQuery库
├── amazeui.js                   # AmazeUI框架
├── swiper.jquery.min.js         # 轮播组件脚本
├── wpyou.js                     # 主题主要脚本
├── zh-tw.js                     # 繁简转换
└── fancybox/                    # 灯箱效果
```

## 资源加载策略
在 [functions.php](mdc:functions.php) 的 `wpyou_enqueue_scripts()` 函数中管理：

### 条件加载
```php
// 政府公开页面专用资源
if(is_page_template('page-gongkai.php') || (get_post_type() == 'opens')) {
    // 加载政府公开专用CSS序列
    wp_enqueue_style('amazeui', get_template_directory_uri() . '/css/gongkai1.css');
    wp_enqueue_style('site', get_template_directory_uri() . '/css/gongkai2.css');
    // ... 其他gongkai系列样式
    
    // 加载政府公开专用JS
    wp_enqueue_script('gongkai', get_template_directory_uri() . '/js/jquery.min.js');
    wp_enqueue_script('amazeui', get_template_directory_uri() . '/js/amazeui.js');
} else {
    // 普通页面资源
    wp_enqueue_style('swiper', get_template_directory_uri() . '/css/swiper.min.css');
    wp_enqueue_style('fancybox', get_template_directory_uri() . '/js/fancybox/jquery.fancybox.css');
}
```

### 脚本依赖管理
```php
// 确保jQuery先加载
wp_enqueue_script('jquery');

// 设置脚本依赖关系
wp_enqueue_script('wpyou-script', 
    get_template_directory_uri() . '/js/wpyou.js',
    array('jquery'), // 依赖jQuery
    '', 
    false // 在头部加载
);
```

## CSS编码规范

### 1. 文件编码
- 使用UTF-8编码
- 统一使用2个空格缩进
- 每个属性占一行

### 2. 命名约定
```css
/* 使用BEM命名法或语义化类名 */
.admin-check-nav {}
.admin-check__item {}
.admin-check__item--active {}

/* 政府网站专用前缀 */
.gov-header {}
.gov-content {}
.gov-footer {}
```

### 3. 样式结构
```css
/* 通用样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
}
```

### 4. 政府网站色彩规范
```css
:root {
    --gov-primary: #c41e3a;    /* 政府红 */
    --gov-secondary: #2c5aa0;  /* 政府蓝 */
    --gov-text: #333333;       /* 主要文字 */
    --gov-border: #e5e5e5;     /* 边框颜色 */
}
```

## JavaScript编码规范

### 1. 代码组织
```javascript
// 使用立即执行函数避免全局污染
(function($) {
    'use strict';
    
    // 行政检查功能模块
    var AdminCheck = {
        init: function() {
            this.bindEvents();
            this.loadInitialData();
        },
        
        bindEvents: function() {
            // 事件绑定
        },
        
        loadInitialData: function() {
            // 初始数据加载
        }
    };
    
    // 文档准备完成后初始化
    $(document).ready(function() {
        AdminCheck.init();
    });
    
})(jQuery);
```

### 2. AJAX请求规范
```javascript
// 标准AJAX请求格式
$.ajax({
    url: ajax_object.ajax_url,
    type: 'POST',
    data: {
        action: 'get_check_data',
        category_id: categoryId,
        department_id: departmentId,
        nonce: ajax_object.nonce
    },
    beforeSend: function() {
        // 显示加载状态
        $('.loading').show();
    },
    success: function(response) {
        if (response.success) {
            // 处理成功响应
            updateCheckList(response.data);
        } else {
            console.error('请求失败:', response.data);
        }
    },
    error: function(xhr, status, error) {
        console.error('AJAX错误:', error);
    },
    complete: function() {
        // 隐藏加载状态
        $('.loading').hide();
    }
});
```

## 性能优化

### 1. CSS优化
- 合并同类型的CSS文件
- 使用CSS压缩
- 避免使用@import
- 优化选择器性能

### 2. JavaScript优化
- 合理使用事件代理
- 避免频繁的DOM操作
- 使用防抖和节流优化事件处理
- 按需加载大型库文件

### 3. 缓存策略
```php
// 为静态资源添加版本号
wp_enqueue_style('theme-style', 
    get_template_directory_uri() . '/css/style.css',
    array(),
    filemtime(get_template_directory() . '/css/style.css')
);
```

## 调试和测试

### 1. 开发环境
- 使用未压缩版本的库文件
- 启用浏览器开发者工具
- 检查控制台错误和警告

### 2. 生产环境
- 使用压缩版本的文件
- 测试不同浏览器的兼容性
- 验证移动端响应式效果

## 兼容性要求
- 支持IE11及以上浏览器
- 移动端兼容iOS Safari和Android Chrome
- 确保在不同分辨率下正常显示
- 政府网站要求支持屏幕阅读器

## 文档要求
- 重要样式修改要添加注释说明
- JavaScript函数要有清晰的注释
- 记录第三方库的版本和用途
- 维护更新日志