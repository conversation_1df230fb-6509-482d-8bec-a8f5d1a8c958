---
alwaysApply: true
description: 中文WordPress开发规范和本地化指南
---

# 中文WordPress开发规范

## 语言和本地化
- 所有用户界面文本使用中文
- 代码注释使用中文
- 变量名可使用英文，但要有中文注释说明
- 数据库字段名使用英文，显示标签使用中文

## 中文字符处理
- 确保数据库使用UTF-8编码
- 处理中文URL时注意编码转换
- 中文搜索功能要考虑分词

## 政府网站特殊要求
- 信息公开栏目结构要符合政府标准
- 行政检查流程要规范化
- 部门名称要使用官方标准称谓

## 用户体验
- 中文排版注意行高和字间距
- 政府网站要求简洁、正式的界面风格
- 考虑老年用户，字体不宜过小

## SEO优化
- 中文关键词优化
- 面包屑导航使用中文
- 页面标题格式：`页面名称 - 网站名称`

## 编码规范
- 文件编码统一使用UTF-8 without BOM
- 代码缩进使用4个空格
- PHP代码遵循WordPress编码标准
- JavaScript代码使用现代ES6+语法

## 性能优化
- 合理使用CDN加速中文字体
- 图片压缩优化
- 启用浏览器缓存
- 数据库查询优化

## 安全考虑
- 输入验证和过滤
- SQL注入防护
- XSS攻击防护
- 文件上传安全检查