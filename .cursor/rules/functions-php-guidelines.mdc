---
description: functions.php文件编辑指南和最佳实践
---

# functions.php 编辑指南

## 文件结构理解
[functions.php](mdc:functions.php) 是主题的核心功能文件，包含：
- 样式和脚本加载
- 自定义文章类型定义
- AJAX处理函数
- 主题配置选项

## 编辑安全规范
1. **备份优先**: 修改前务必备份原文件
2. **增量修改**: 避免大范围重写，采用增量添加方式
3. **代码注释**: 所有新增功能必须添加中文注释
4. **错误处理**: 添加适当的错误检查和异常处理

## 常见修改区域

### 1. 样式和脚本加载
位置：`wpyou_enqueue_scripts()` 函数
```php
// 添加新的CSS/JS文件时的标准格式
wp_register_style('new-style', get_template_directory_uri() . '/css/new-style.css');
wp_enqueue_style('new-style');
```

### 2. AJAX处理函数
添加新的AJAX功能时：
```php
// 为登录用户添加AJAX钩子
add_action('wp_ajax_action_name', 'callback_function');
// 为未登录用户添加AJAX钩子  
add_action('wp_ajax_nopriv_action_name', 'callback_function');
```

### 3. 自定义文章类型
位置：主题初始化区域
- 确保使用中文标签和描述
- 设置正确的capability_type
- 配置合适的supports数组

### 4. 元字段和自定义字段
使用CMB2框架添加元字段：
- 配置文件位于 `options/` 目录
- 遵循现有命名约定

## 代码质量要求
- **缩进**: 使用4个空格缩进
- **命名**: 函数名使用下划线分隔，变量名要有意义
- **安全**: 所有用户输入必须进行sanitize处理
- **性能**: 避免在函数中进行重复的数据库查询

## 测试要求
修改后必须测试：
- 前端页面正常显示
- 后台功能正常工作
- 无PHP错误或警告
- AJAX功能正常响应

## 常见问题避免
- 不要在functions.php中直接输出HTML
- 避免使用全局变量污染
- 不要删除现有的钩子和过滤器
- 确保所有字符串都是UTF-8编码