---
globs: *.php
---

# PHP编码规范

## 基础编码标准
遵循WordPress PHP编码标准，结合政府网站开发要求。

## 文件编码和格式
- 文件编码：UTF-8 without BOM
- 行结束符：Unix LF (\n)
- 缩进：4个空格，不使用Tab
- 每行最大长度：120字符

## PHP标签规范
```php
<?php
// 文件开头使用完整PHP标签
// 纯PHP文件末尾不要使用闭合标签 ?>
```

## 命名约定

### 函数命名
```php
// 使用下划线分隔的小写字母
function get_admin_check_data() {
    // 函数内容
}

// 主题专用函数添加前缀
function popunion_custom_function() {
    // 避免命名冲突
}
```

### 变量命名
```php
// 使用有意义的变量名
$check_categories = array();
$current_user_id = get_current_user_id();
$post_meta_data = get_post_meta($post_id);
```

### 常量命名
```php
// 使用大写字母和下划线
define('POPUNION_VERSION', '1.0.0');
define('ADMIN_CHECK_POST_TYPE', 'admin_check');
```

## 安全编码实践

### 数据验证和过滤
```php
// 验证用户输入
$category_id = absint($_POST['category_id']);
$search_term = sanitize_text_field($_POST['search']);

// 输出转义
echo esc_html($user_input);
echo esc_url($link_url);
echo esc_attr($attribute_value);
```

### SQL查询安全
```php
global $wpdb;
// 使用准备语句
$results = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->posts} WHERE post_title LIKE %s",
    '%' . $wpdb->esc_like($search_term) . '%'
));
```

### Nonce验证
```php
// AJAX请求验证
if (!wp_verify_nonce($_POST['nonce'], 'admin_check_action')) {
    wp_die('安全验证失败');
}
```

## 错误处理
```php
// 使用WordPress错误处理
if (is_wp_error($result)) {
    error_log('行政检查数据获取失败: ' . $result->get_error_message());
    return false;
}

// 添加调试信息（仅在调试模式下）
if (WP_DEBUG) {
    error_log('调试信息: ' . print_r($debug_data, true));
}
```

## 注释规范
```php
/**
 * 获取行政检查分类数据
 * 
 * @param int $department_id 部门ID
 * @param string $region 地区代码
 * @return array|false 检查分类数组或失败时返回false
 */
function get_check_categories($department_id = 0, $region = '') {
    // 实现代码
}

// 单行注释使用中文说明业务逻辑
// 获取当前用户的检查权限
$user_permissions = get_user_check_permissions();
```

## 数组和对象
```php
// 数组格式化
$check_data = array(
    'id'          => $check_id,
    'title'       => $check_title,
    'category'    => $category_name,
    'department'  => $department_name,
    'region'      => $region_code,
);

// 关联数组查询结果
$query_args = array(
    'post_type'      => 'admin_check',
    'posts_per_page' => 20,
    'meta_query'     => array(
        array(
            'key'     => 'check_department',
            'value'   => $department_id,
            'compare' => '='
        )
    )
);
```

## 条件语句格式
```php
// if语句格式
if ($condition1 && $condition2) {
    // 执行代码
} elseif ($condition3) {
    // 其他条件
} else {
    // 默认情况
}

// 三元运算符
$display_text = !empty($custom_text) ? $custom_text : '默认文本';
```

## WordPress特定规范
```php
// 钩子和过滤器
add_action('wp_ajax_get_check_data', 'handle_check_data_request');
add_filter('the_content', 'modify_check_content');

// 条件标签使用
if (is_page_template('page-admin-check-nav.php')) {
    // 特定页面逻辑
}

// 自定义查询后重置
wp_reset_postdata();
```

## 性能优化
```php
// 避免在循环中进行数据库查询
// 使用wp_cache_*函数进行缓存
$cache_key = 'check_categories_' . $department_id;
$categories = wp_cache_get($cache_key);
if (false === $categories) {
    $categories = get_check_categories_from_db($department_id);
    wp_cache_set($cache_key, $categories, '', 3600);
}
```