---
description: WordPress页面模板文件命名和结构约定
---

# 页面模板约定规范

## 模板文件命名规范
遵循WordPress标准和项目约定：

### 通用模板
- [index.php](mdc:index.php) - 默认模板
- [header.php](mdc:header.php) / [footer.php](mdc:footer.php) - 头部底部模板
- [sidebar.php](mdc:sidebar.php) - 侧边栏模板

### 特殊页面模板  
- `page-{slug}.php` - 特定页面模板，如 [page-gongkai.php](mdc:page-gongkai.php)
- `single-{post_type}.php` - 自定义文章类型单页模板
- `archive-{post_type}.php` - 自定义文章类型归档模板
- `taxonomy-{taxonomy}.php` - 自定义分类法模板

### 政府专用模板
- [page-gongkai.php](mdc:page-gongkai.php) - 政府信息公开页面
- [page-admin-check-nav.php](mdc:page-admin-check-nav.php) - 行政检查导航
- [page-admin-check-list.php](mdc:page-admin-check-list.php) - 行政检查列表
- [archive-admin_check.php](mdc:archive-admin_check.php) - 行政检查归档
- [single-admin_check.php](mdc:single-admin_check.php) - 行政检查详情
- [taxonomy-check_category.php](mdc:taxonomy-check_category.php) - 检查类别分类

## 模板结构要求

### 1. 文件头部
```php
<?php
/*
Template Name: 模板中文名称
Description: 模板功能描述
*/
```

### 2. 标准结构
```php
<?php get_header(); ?>
<!-- 页面主要内容 -->
<div class="main-content">
    <!-- 内容区域 -->
</div>
<?php get_footer(); ?>
```

### 3. 条件加载
根据页面类型加载不同的头部和底部：
```php
// 政府公开页面使用专用头部底部
if(is_page_template('page-gongkai.php')) {
    get_header('gongkai');
    // ...
    get_footer('gongkai');
}
```

## CSS/JS资源管理
- 在 [functions.php](mdc:functions.php) 中的 `wpyou_enqueue_scripts()` 函数内注册样式
- 使用条件判断为特定模板加载专用资源
- 政府公开页面有专门的CSS文件序列

## 响应式设计要求
- 所有模板必须支持移动端显示
- 使用Bootstrap或类似框架确保兼容性
- 关键信息在小屏幕上要保持可读性

## SEO优化
- 每个模板都要设置合适的页面标题
- 使用面包屑导航 [breadcrumb.php](mdc:breadcrumb.php)
- 确保关键内容的HTML语义化

## 无障碍访问
政府网站必须符合无障碍标准：
- 使用语义化HTML标签
- 为图片添加alt属性
- 确保键盘导航可用
- 提供合适的颜色对比度

## 模板测试清单
新建或修改模板后检查：
- [ ] 在不同设备和浏览器上显示正常
- [ ] 页面加载速度符合要求
- [ ] SEO信息正确设置
- [ ] 无JavaScript错误
- [ ] 符合政府网站设计规范