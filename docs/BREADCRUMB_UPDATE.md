# 详情页面包屑导航位置调整

## 修改需求
用户要求将详情页的面包屑导航移动到文章标题上方，并且去掉"检查项目详情"副标题。

## 修改内容

### 1. 移除头部的面包屑导航和副标题
**修改前 (头部区域):**
```html
<div class="hero-overlay">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
        <a href="...">首页</a>
        <span class="separator">></span>
        <a href="...">行政检查信息公开</a>
        <span class="separator">></span>
        <span><?php the_title(); ?></span>
    </div>
    <!-- 主标题 -->
    <div class="main-title">
        <h1>乌鲁木齐县涉企行政检查专栏</h1>
        <div class="subtitle">检查项目详情</div>
    </div>
</div>
```

**修改后 (头部区域):**
```html
<div class="hero-overlay">
    <!-- 主标题 -->
    <div class="main-title">
        <h1>乌鲁木齐县涉企行政检查专栏</h1>
    </div>
</div>
```

### 2. 在文章标题上方添加面包屑导航
**修改前 (内容区域):**
```html
<article class="check-detail">
    <!-- 文章标题 -->
    <header class="article-header">
        <h1 class="article-title"><?php the_title(); ?></h1>
```

**修改后 (内容区域):**
```html
<article class="check-detail">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
        <div class="breadcrumb-nav">
            <a href="<?php echo home_url(); ?>">首页</a>
            <span class="separator">></span>
            <a href="<?php echo home_url('/admin-check-nav/'); ?>">行政检查信息公开</a>
            <span class="separator">></span>
            <span><?php the_title(); ?></span>
        </div>
    </div>
    
    <!-- 文章标题 -->
    <header class="article-header">
        <h1 class="article-title"><?php the_title(); ?></h1>
```

### 3. 更新CSS样式

#### 新的面包屑导航样式
```css
/* 面包屑导航 */
.breadcrumb-section {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 15px 20px;
    margin: -20px -20px 20px -20px;
    border-radius: 8px 8px 0 0;
}

.breadcrumb-nav {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

.breadcrumb-nav a {
    color: #4a90e2;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-nav a:hover {
    color: #2563eb;
    text-decoration: none;
}

.breadcrumb-nav .separator {
    margin: 0 8px;
    color: #d1d5db;
}
```

#### 简化的主标题样式
```css
/* 主标题 */
.main-title {
    text-align: center;
    margin-top: 100px;
}

.main-title h1 {
    font-size: 48px;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 3px 6px rgba(0,0,0,0.6);
    letter-spacing: 2px;
    line-height: 1.2;
}
```

## 修改结果

### ✅ 实现的效果
1. **头部区域** - 只显示"乌鲁木齐县涉企行政检查专栏"主标题，去掉了"检查项目详情"副标题
2. **内容区域** - 面包屑导航现在位于文章标题上方，采用浅灰色背景
3. **导航路径** - 显示：首页 > 行政检查信息公开 > [文章标题]

### 🎨 视觉效果
- 面包屑导航有独立的浅灰色背景区域
- 与文章内容有清晰的视觉分隔
- 保持了与主题的设计一致性
- 链接颜色和悬停效果正常

### 📱 布局优化
- 面包屑导航现在更接近文章内容
- 头部区域更加简洁清爽
- 信息层次更加清晰明确

---
**修改状态**: ✅ 已完成  
**测试状态**: ✅ 语法检查通过  
**视觉效果**: ✅ 符合预期
