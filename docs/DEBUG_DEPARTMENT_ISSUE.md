# 政府部门点击问题调试指南

## 问题描述
点击左侧政府部门时，右侧列表显示的是所有政府部门的标题，而不是该部门下的检查项目。

## 添加的调试功能

### 前端调试 (page-admin-check-nav.php)
在点击部门的JavaScript代码中添加了以下调试信息：

```javascript
// 调试信息
console.log('点击部门:', departmentName, '部门ID:', departmentId, '分类ID:', selectedCategory);
console.log('自动选择第一个分类:', selectedCategory);
console.log('发送参数:', params);
```

**查看方法**: 
1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签
3. 点击左侧任意政府部门
4. 查看控制台输出的调试信息

### 后端调试 (functions.php)
在 `get_check_items_by_filters_ajax()` 函数中添加了以下调试信息：

```php
// 调试信息
error_log('AJAX请求参数: cate_id=' . $cate_id . ', dept_id=' . $dept_id . ', department=' . $department . ', region_name=' . $region_name);
error_log('查找部门 ID: ' . $dept_id);
error_log('找到部门: ' . $dept_term->name . ' (term_id: ' . $dept_term->term_id . ')');
error_log('未找到部门 ID: ' . $dept_id);
```

**查看方法**: 
1. 查看WordPress错误日志文件 (通常在 wp-content/debug.log)
2. 或在服务器错误日志中查看
3. 点击部门后查看相关日志条目

## 预期的调试输出

### 正常情况下应该看到：
1. **前端控制台**:
   ```
   点击部门: 教育局 部门ID: 123 分类ID: 1
   发送参数: {cate_id: 1, dept_id: 123}
   ```

2. **后端日志**:
   ```
   AJAX请求参数: cate_id=1, dept_id=123, department=, region_name=
   查找部门 ID: 123
   找到部门: 教育局 (term_id: 456)
   ```

### 可能的问题情况：

#### 情况1: 部门ID为空或undefined
```
点击部门: 教育局 部门ID: undefined 分类ID: 1
```
**原因**: `data-department` 属性没有正确设置
**解决**: 检查 `get_departments_by_region_ajax()` 函数是否正确返回了 `department_id`

#### 情况2: 后端找不到部门
```
AJAX请求参数: cate_id=1, dept_id=123, department=, region_name=
查找部门 ID: 123
未找到部门 ID: 123
```
**原因**: 部门的 `department_id` meta字段没有正确设置
**解决**: 需要为每个部门设置正确的 `department_id` meta值

#### 情况3: 分类ID为空
```
点击部门: 教育局 部门ID: 123 分类ID: undefined
```
**原因**: 没有选择分类或分类ID获取失败
**解决**: 确保页面加载时正确初始化第一个分类

## 解决步骤

### 步骤1: 验证前端数据
1. 打开浏览器开发者工具
2. 点击政府部门
3. 检查控制台输出的部门ID是否正确

### 步骤2: 验证后端处理
1. 查看错误日志
2. 确认AJAX请求参数是否正确传递
3. 确认是否找到了对应的部门

### 步骤3: 检查数据库
如果部门ID正确传递但后端找不到部门，需要检查：
```sql
-- 检查部门term meta
SELECT * FROM wp_termmeta WHERE meta_key = 'department_id';

-- 检查部门taxonomy
SELECT * FROM wp_terms WHERE name LIKE '%教育局%';
```

### 步骤4: 手动设置部门ID (如果需要)
如果发现部门缺少 `department_id` meta，可以手动设置：
```php
// 在WordPress后台或临时脚本中执行
$departments = get_terms(array('taxonomy' => 'department'));
foreach ($departments as $dept) {
    if (!get_term_meta($dept->term_id, 'department_id', true)) {
        update_term_meta($dept->term_id, 'department_id', $dept->term_id);
    }
}
```

## 完成调试后
找到并解决问题后，记得删除调试代码以保持代码整洁。

---
**调试状态**: ✅ 已添加调试代码  
**下一步**: 测试并分析调试输出结果
