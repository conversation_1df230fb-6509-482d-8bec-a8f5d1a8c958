<?php
/*
Template Name: 修复分类链接
*/

// 检查管理员权限
if (!current_user_can('manage_options')) {
    wp_die('您没有权限访问此页面');
}

// 处理修复操作
if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'flush_permalinks':
            // 刷新固定链接结构
            flush_rewrite_rules(true);
            $message = '✅ 固定链接已刷新！';
            break;
            
        case 'fix_taxonomy_rewrite':
            // 重新注册分类法并刷新重写规则
            register_check_category_taxonomy();
            register_region_type_taxonomy();
            register_department_taxonomy();
            flush_rewrite_rules(true);
            $message = '✅ 分类法重写规则已修复！';
            break;
            
        case 'regenerate_all':
            // 完全重新生成所有重写规则
            register_administrative_check_post_type();
            register_check_category_taxonomy();
            register_region_type_taxonomy();
            register_department_taxonomy();
            associate_admin_check_taxonomies();
            flush_rewrite_rules(true);
            $message = '✅ 所有重写规则已重新生成！';
            break;
    }
}

// 获取分类链接状态
function get_category_links_status() {
    $status = array();
    
    // 获取检查类别
    $check_categories = get_terms(array(
        'taxonomy' => 'check_category',
        'hide_empty' => false
    ));
    
    $status['check_categories'] = array();
    if (!empty($check_categories)) {
        foreach ($check_categories as $category) {
            $link = get_term_link($category);
            $status['check_categories'][] = array(
                'name' => $category->name,
                'slug' => $category->slug,
                'link' => is_wp_error($link) ? '错误: ' . $link->get_error_message() : $link,
                'is_error' => is_wp_error($link)
            );
        }
    }
    
    // 检查固定链接设置
    $permalink_structure = get_option('permalink_structure');
    $status['permalink_structure'] = $permalink_structure;
    
    // 检查重写规则
    global $wp_rewrite;
    $status['rewrite_rules'] = $wp_rewrite->wp_rewrite_rules();
    
    return $status;
}

$status = get_category_links_status();
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>修复分类链接</title>
    <?php wp_head(); ?>
    <style>
        .fix-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .message { 
            background: #d4edda; 
            color: #155724; 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .code-block {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .link-test {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #ddd;
            padding-left: 10px;
        }
        .link-test.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .link-test.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>

<div class="fix-container">
    <h1>🔗 分类链接修复工具</h1>
    
    <?php if (isset($message)): ?>
        <div class="message"><?php echo esc_html($message); ?></div>
    <?php endif; ?>
    
    <!-- 当前状态 -->
    <div class="card">
        <h2>📊 当前状态诊断</h2>
        
        <h3>固定链接结构</h3>
        <div class="code-block">
            <?php echo $status['permalink_structure'] ? $status['permalink_structure'] : '默认结构 (?p=123)'; ?>
        </div>
        
        <h3>检查类别链接状态</h3>
        <?php if (!empty($status['check_categories'])): ?>
            <?php foreach ($status['check_categories'] as $category): ?>
                <div class="link-test <?php echo $category['is_error'] ? 'error' : 'success'; ?>">
                    <strong><?php echo esc_html($category['name']); ?></strong> (<?php echo esc_html($category['slug']); ?>)
                    <br>
                    链接: <?php echo $category['is_error'] ? '<span class="status-bad">' . esc_html($category['link']) . '</span>' : '<a href="' . esc_url($category['link']) . '" target="_blank">' . esc_html($category['link']) . '</a>'; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p class="status-warning">⚠️ 没有找到检查类别</p>
        <?php endif; ?>
        
        <h3>分类法注册状态</h3>
        <table>
            <tr>
                <th>分类法</th>
                <th>注册状态</th>
                <th>公开访问</th>
                <th>重写规则</th>
            </tr>
            <?php
            $taxonomies = array(
                'check_category' => '检查类别',
                'region_type' => '区域类型', 
                'department' => '部门机构'
            );
            foreach ($taxonomies as $taxonomy => $label):
                $tax_object = get_taxonomy($taxonomy);
                $exists = taxonomy_exists($taxonomy);
            ?>
            <tr>
                <td><?php echo $label; ?> (<?php echo $taxonomy; ?>)</td>
                <td class="<?php echo $exists ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $exists ? '✅ 已注册' : '❌ 未注册'; ?>
                </td>
                <td>
                    <?php if ($exists && $tax_object): ?>
                        <span class="<?php echo $tax_object->public ? 'status-good' : 'status-warning'; ?>">
                            <?php echo $tax_object->public ? '✅ 公开' : '⚠️ 非公开'; ?>
                        </span>
                    <?php else: ?>
                        <span class="status-bad">❌ 无法检查</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($exists && $tax_object && isset($tax_object->rewrite['slug'])): ?>
                        <code><?php echo $tax_object->rewrite['slug']; ?></code>
                    <?php else: ?>
                        <span class="status-warning">⚠️ 未设置</span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <!-- 修复操作 -->
    <div class="card">
        <h2>🛠️ 修复操作</h2>
        <p>请按顺序尝试以下修复方法：</p>
        
        <div style="margin: 20px 0;">
            <h3>步骤1: 刷新固定链接</h3>
            <p>这是最常见的解决方法，通常能解决大部分链接问题。</p>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="flush_permalinks">
                <button type="submit" class="btn btn-success">刷新固定链接</button>
            </form>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>步骤2: 修复分类法重写规则</h3>
            <p>重新注册分类法并刷新重写规则。</p>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="fix_taxonomy_rewrite">
                <button type="submit" class="btn btn-warning">修复分类法重写</button>
            </form>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>步骤3: 完全重新生成（谨慎使用）</h3>
            <p>重新注册所有自定义文章类型和分类法，完全重建重写规则。</p>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="regenerate_all">
                <button type="submit" class="btn btn-danger" onclick="return confirm('这将重新生成所有重写规则，可能影响其他自定义链接。确定继续吗？')">
                    完全重新生成
                </button>
            </form>
        </div>
    </div>
    
    <!-- 手动检查 -->
    <div class="card">
        <h2>🔍 手动检查</h2>
        
        <h3>测试分类链接</h3>
        <?php if (!empty($status['check_categories'])): ?>
            <p>点击以下链接测试是否正常工作：</p>
            <ul>
                <?php foreach ($status['check_categories'] as $category): ?>
                    <?php if (!$category['is_error']): ?>
                        <li>
                            <a href="<?php echo esc_url($category['link']); ?>" target="_blank">
                                <?php echo esc_html($category['name']); ?>
                            </a>
                            <small>(<?php echo esc_html($category['link']); ?>)</small>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
        
        <h3>WordPress后台检查</h3>
        <ul>
            <li><a href="<?php echo admin_url('options-permalink.php'); ?>" target="_blank">固定链接设置</a> - 检查固定链接结构</li>
            <li><a href="<?php echo admin_url('edit-tags.php?taxonomy=check_category'); ?>" target="_blank">检查类别管理</a> - 查看所有检查类别</li>
            <li><a href="<?php echo admin_url('edit.php?post_type=admin_check'); ?>" target="_blank">行政检查文章</a> - 查看检查项目</li>
        </ul>
    </div>
    
    <!-- 常见问题 -->
    <div class="card">
        <h2>❓ 常见问题解决</h2>
        
        <h3>问题1: 分类页面显示404错误</h3>
        <ul>
            <li>✅ 点击"刷新固定链接"按钮</li>
            <li>✅ 检查分类法是否正确注册</li>
            <li>✅ 确认分类模板文件存在 (taxonomy-check_category.php)</li>
        </ul>
        
        <h3>问题2: 分类链接格式不正确</h3>
        <ul>
            <li>✅ 检查固定链接结构设置</li>
            <li>✅ 确认分类法的rewrite配置正确</li>
            <li>✅ 点击"修复分类法重写"按钮</li>
        </ul>
        
        <h3>问题3: 分类页面内容为空</h3>
        <ul>
            <li>✅ 确认该分类下有文章</li>
            <li>✅ 检查文章是否已发布</li>
            <li>✅ 确认分类关联正确</li>
        </ul>
    </div>
    
    <div class="card">
        <h2>📝 操作记录</h2>
        <p>修复完成后，建议测试以下链接：</p>
        <ul>
            <li>行政检查导航页面: <a href="<?php echo home_url('/admin-check-nav/'); ?>" target="_blank">/admin-check-nav/</a></li>
            <li>行政检查归档页面: <a href="<?php echo home_url('/admin-check/'); ?>" target="_blank">/admin-check/</a></li>
            <li>检查类别归档: <a href="<?php echo home_url('/check-category/'); ?>" target="_blank">/check-category/</a></li>
        </ul>
    </div>
</div>

<?php wp_footer(); ?>
</body>
</html>
