<?php
ob_start();
include_once("options/widget_home.php");
include_once("options/widget.php");
include_once('options/relevant.php');
include_once('options/extra.php');
include_once('options/options.php');
include_once('post-meta-box.php');
//Load Custom css and js
function wpyou_enqueue_scripts() {
  wp_enqueue_style( 'wpyou-style', get_stylesheet_uri() );
	//gongkai
  if(is_page_template( $template = 'page-gongkai.php' )||(get_post_type() == 'opens')){
    wp_register_style('amazeui',  get_template_directory_uri() .'/css/gongkai1.css');
    wp_enqueue_style( 'amazeui');
     wp_register_style('site',  get_template_directory_uri() .'/css/gongkai2.css');
    wp_enqueue_style( 'site');
     wp_register_style('public',  get_template_directory_uri() .'/css/gongkai3.css');
    wp_enqueue_style( 'public');
    wp_register_style('fontawesome',  get_template_directory_uri() .'/css/gongkai4.css');
    wp_enqueue_style( 'fontawesome');
     wp_register_style('site_xxgk',  get_template_directory_uri() .'/css/gongkai5.css');
    wp_enqueue_style( 'site_xxgk');
     wp_register_style('standard-gk',  get_template_directory_uri() .'/css/gongkai6.css');
    wp_enqueue_style( 'standard-gk');
  }else{//原来的 
    
    wp_enqueue_style( 'swiper', get_template_directory_uri() . '/css/swiper.min.css');
    wp_enqueue_style( 'fancybox', get_template_directory_uri() . '/js/fancybox/jquery.fancybox.css');
  }


//	wp_register_style('custom-color',  get_template_directory_uri() .'/style-custom-color.php');
//	wp_enqueue_style( 'custom-color');
	
	wp_enqueue_script('jquery');
  wp_enqueue_script( 'swiper', get_template_directory_uri() . '/js/swiper.jquery.min.js','','', false );
  wp_enqueue_script( 'fancybox', get_template_directory_uri() . '/js/fancybox/jquery.fancybox.js');
  wp_enqueue_script( 'wpyou-script', get_template_directory_uri() . '/js/wpyou.js','','', false );
  wp_enqueue_script( 'zh-tw', get_template_directory_uri() . '/js/zh-tw.js','','', false );

  if(is_page_template( $template = 'page-gongkai.php' )||(get_post_type() == 'opens')){
    wp_register_script('gongkai',  get_template_directory_uri() .'/js/jquery.min.js');
    wp_enqueue_script( 'gongkai');
    wp_register_script('amazeui',  get_template_directory_uri() .'/js/amazeui.js');
    wp_enqueue_script( 'amazeui');
  }

  
	
  if (is_singular()&&(!is_page_template( $template = 'page-gongkai.php' ))) {	
		wp_enqueue_script( 'qrcode', get_template_directory_uri() . '/js/jquery.qrcode.min.js','','', false );
	}
}
add_action( 'wp_enqueue_scripts', 'wpyou_enqueue_scripts' );
//Thumbnail
if ( function_exists( 'add_theme_support' ) )
	add_theme_support( 'post-thumbnails' );
// CustomBackground
if ( function_exists('add_theme_support')) { add_theme_support( 'custom-background' ); }
// CustomMenus
if ( function_exists('register_nav_menus')) { register_nav_menus(array('primary' => '导航菜单'));}
// RemoveEmojiIcons
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');
//ModifyArchivesWidgetsDateFormat
add_filter('get_archives_link', 'translate_archive_month');
function translate_archive_month($list) {
  $patterns = array(
    '/零/', '/一/', '/二/', '/三/', '/四/', '/五/', '/六/',
    '/七/', '/八/', '/九/', '/十/'
  );
  $replacements = array(
    '0', '1', '2', '3', '4', '5', '6',
    '7', '8', '9', '1'
  );
  $list = preg_replace($patterns, $replacements, $list);
return $list; 
}
//添加自定义文章类型
require_once(get_template_directory() . '/gongkai.php');// Ebooks

//Remove <br> from Gallerylist 
add_theme_support('html5', array('gallery'));
//RemoveWPGalleryDefaultStyle
add_filter( 'use_default_gallery_style', '__return_false' );
// Custom Comment
function custom_comment($comment, $args, $depth) {
   $GLOBALS['comment'] = $comment; ?>
   <li <?php comment_class(); ?> id="li-comment-<?php comment_ID() ?>">
     <div id="comment-<?php comment_ID(); ?>">
         <div class="comment-author vcard">
                <?php /*?><?php echo get_avatar($comment,$size='28'); ?><?php */?>
                <div class="author_info">
					<?php printf(__('<cite class="fn">%s</cite>'), get_comment_author_link()) ?>
                    <em><?php printf(__('%1$s at %2$s'), get_comment_date('Y-m-d '),  get_comment_time(' H:i:s')) ?></em> <?php edit_comment_link(__('(Edit)'),'  ','') ?>
                </div>
                <div class="reply">
			   		<?php comment_reply_link(array_merge( $args, array('depth' => $depth, 'max_depth' => $args['max_depth']))) ?>
              	</div>
          </div>
		  <?php if ($comment->comment_approved == '0') : ?>
             <em><?php _e('Your comment is awaiting moderation.') ?></em>
             <br />
          <?php endif; ?>
      		<div class="comment-content"><?php comment_text() ?></div>
     </div>
<?php } 
//自定义文章类型别名
add_action( 'save_post', 'using_id_as_slug', 10, 2 );
function using_id_as_slug($post_id, $post){
 global $post_type;
 if($post_type=='opens'){ //只对文章生效
  // 如果是文章的版本，不生效
  if (wp_is_post_revision($post_id))
   return false;
  // 取消挂载该函数，防止无限循环
  remove_action('save_post', 'using_id_as_slug' );
  // 使用文章ID作为文章的别名
  wp_update_post(array('ID' => $post_id, 'post_name' => 'openinfo'.$post_id ));
  // 重新挂载该函数
  add_action('save_post', 'using_id_as_slug' );

 }
}
//老文章批量修改别名
function Bing_post_name_id(){
query_posts(array('post_type' => 'opens','posts_per_page' => '-1'));
while( have_posts() ){
the_post();
$post_id = $GLOBALS['post']->ID;
wp_update_post( array(
'ID' => $post_id,
'post_name' => 'openinfo'.$post_id
) );
}
wp_reset_query();
}
if( $_GET['post_name_id'] == 'yes' ) add_action( 'init', 'Bing_post_name_id' );

// // 注册“组织单位”自定义文章类型
// function register_organization_unit() {
//   register_post_type('organization_unit', array(
//       'labels' => array(
//           'name' => '组织单位',
//           'singular_name' => '组织单位',
//           'add_new' => '添加新单位',
//           'add_new_item' => '添加新组织单位',
//           'edit_item' => '编辑组织单位',
//           'new_item' => '新组织单位',
//           'view_item' => '查看组织单位',
//           'search_items' => '搜索组织单位',
//           'not_found' => '未找到组织单位',
//           'not_found_in_trash' => '回收站中未找到组织单位',
//           'menu_name' => '组织单位',
//       ),
//       'public' => true,
//       'show_in_menu' => true, // 显示在后台菜单
//       'menu_position' => 5,   // 在菜单中的位置（靠近文章）
//       'menu_icon' => 'dashicons-building', // 图标
//       'has_archive' => true,  // 启用归档页面
//       'supports' => array('title', 'editor', 'custom-fields'), // 添加自定义字段支持
//       'taxonomies' => array('org_type'), // 关联分类法
//       'rewrite' => array('slug' => 'org-unit'),
//       'show_in_rest' => true, // 启用REST API支持
//       'publicly_queryable' => true, // 允许前端查询
//       'capability_type' => 'post',
//       'hierarchical' => false,
//       'exclude_from_search' => false,
//       'show_ui' => true,
//       'show_in_admin_bar' => true,
//       'show_in_nav_menus' => true,
//       'can_export' => true,
//   ));
// }
// add_action('init', 'register_organization_unit');

// // 注册组织类型分类法
// function register_org_type_taxonomy() {
//   register_taxonomy('org_type', 'organization_unit', array(
//       'labels' => array(
//           'name' => '组织类型',
//           'singular_name' => '组织类型',
//           'search_items' => '搜索组织类型',
//           'all_items' => '所有组织类型',
//           'edit_item' => '编辑组织类型',
//           'update_item' => '更新组织类型',
//           'add_new_item' => '添加新组织类型',
//           'new_item_name' => '新组织类型名称',
//           'menu_name' => '组织类型',
//       ),
//       'public' => true,
//       'hierarchical' => false,
//       'show_ui' => true,
//       'show_admin_column' => true,
//       'show_in_menu' => true,
//       'show_in_nav_menus' => true,
//       'show_tagcloud' => true,
//       'show_in_rest' => true,
//       'rewrite' => array('slug' => 'org-type'),
//   ));
// }
// add_action('init', 'register_org_type_taxonomy');

// // 手动关联分类法到自定义文章类型
// function associate_org_type_taxonomy() {
//     register_taxonomy_for_object_type('org_type', 'organization_unit');
// }
// add_action('init', 'associate_org_type_taxonomy', 99); // 优先级设为99，确保在其他注册完成后执行

// // 刷新重写规则，确保自定义文章类型和分类法的URL正常工作
// function flush_rewrite_rules_on_activation() {
//     register_organization_unit();
//     register_org_type_taxonomy();
//     flush_rewrite_rules();
// }
// // 当主题激活时刷新重写规则
// add_action('after_switch_theme', 'flush_rewrite_rules_on_activation');

// // 添加组织单位到主查询中（可选）
// function include_organization_unit_in_home($query) {
//     if (!is_admin() && $query->is_main_query()) {
//         if (is_home()) {
//             // 如果你想在首页显示组织单位，取消下面这行的注释
//             // $query->set('post_type', array('post', 'organization_unit'));
//         }
//     }
// }
// add_action('pre_get_posts', 'include_organization_unit_in_home');

// ========== 行政检查系统 ==========

// 注册"行政检查"自定义文章类型
function register_administrative_check_post_type() {
    register_post_type('admin_check', array(
        'labels' => array(
            'name' => '行政检查',
            'singular_name' => '行政检查',
            'add_new' => '添加检查项目',
            'add_new_item' => '添加新检查项目',
            'edit_item' => '编辑检查项目',
            'new_item' => '新检查项目',
            'view_item' => '查看检查项目',
            'search_items' => '搜索检查项目',
            'not_found' => '未找到检查项目',
            'not_found_in_trash' => '回收站中未找到检查项目',
            'menu_name' => '行政检查',
        ),
        'public' => true,
        'show_in_menu' => true,
        'menu_position' => 6,
        'menu_icon' => 'dashicons-clipboard',
        'has_archive' => true,
        'supports' => array('title', 'editor', 'custom-fields', 'excerpt'),
        'taxonomies' => array('check_category', 'region_type', 'department'),
        'rewrite' => array('slug' => 'admin-check'),
        'show_in_rest' => true,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'hierarchical' => false,
        'show_ui' => true,
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
    ));
}
add_action('init', 'register_administrative_check_post_type');

// 注册"检查类别"分类法（第一级）
function register_check_category_taxonomy() {
    register_taxonomy('check_category', 'admin_check', array(
        'labels' => array(
            'name' => '检查类别',
            'singular_name' => '检查类别',
            'search_items' => '搜索检查类别',
            'all_items' => '所有检查类别',
            'edit_item' => '编辑检查类别',
            'update_item' => '更新检查类别',
            'add_new_item' => '添加新检查类别',
            'new_item_name' => '新检查类别名称',
            'menu_name' => '检查类别',
        ),
        'public' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'check-category'),
    ));
}
add_action('init', 'register_check_category_taxonomy');

// 后台：在“检查类别”新增页面增加排序字段
function add_check_category_sort_add_field() {
    ?>
    <div class="form-field">
        <label for="check_category_sort">排序（sort）</label>
        <input type="number" name="check_category_sort" id="check_category_sort" value="" min="0" step="1" />
        <p class="description">用于前端展示顺序，数字越小越靠前。</p>
    </div>
    <?php
}
add_action('check_category_add_form_fields', 'add_check_category_sort_add_field');

// 后台：在“检查类别”编辑页面增加排序字段
function add_check_category_sort_edit_field($term) {
    $current_sort = get_term_meta($term->term_id, 'sort', true);
    ?>
    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="check_category_sort">排序（sort）</label>
        </th>
        <td>
            <input type="number" name="check_category_sort" id="check_category_sort" value="<?php echo esc_attr($current_sort); ?>" min="0" step="1" />
            <p class="description">用于前端展示顺序，数字越小越靠前。</p>
        </td>
    </tr>
    <?php
}
add_action('check_category_edit_form_fields', 'add_check_category_sort_edit_field');

// 保存“检查类别”的排序字段
function save_check_category_sort_meta($term_id) {
    if (isset($_POST['check_category_sort'])) {
        $sort_value = sanitize_text_field($_POST['check_category_sort']);
        if ($sort_value === '' || $sort_value === null) {
            delete_term_meta($term_id, 'sort');
        } else {
            update_term_meta($term_id, 'sort', intval($sort_value));
        }
    }
}
add_action('created_check_category', 'save_check_category_sort_meta');
add_action('edited_check_category', 'save_check_category_sort_meta');

// 列表页显示“排序”列
function add_check_category_columns($columns) {
    $columns['sort'] = '排序';
    return $columns;
}
add_filter('manage_edit-check_category_columns', 'add_check_category_columns');

function render_check_category_custom_column($content, $column_name, $term_id) {
    if ($column_name === 'sort') {
        $sort = get_term_meta($term_id, 'sort', true);
        $content = $sort !== '' ? intval($sort) : '<span style="color:#ccc;">未设置</span>';
    }
    return $content;
}
add_filter('manage_check_category_custom_column', 'render_check_category_custom_column', 10, 3);

// 注册"区域类型"分类法（第二级）
function register_region_type_taxonomy() {
    register_taxonomy('region_type', 'admin_check', array(
        'labels' => array(
            'name' => '区域类型',
            'singular_name' => '区域类型',
            'search_items' => '搜索区域类型',
            'all_items' => '所有区域类型',
            'edit_item' => '编辑区域类型',
            'update_item' => '更新区域类型',
            'add_new_item' => '添加新区域类型',
            'new_item_name' => '新区域类型名称',
            'menu_name' => '区域类型',
        ),
        'public' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'region-type'),
    ));
}
add_action('init', 'register_region_type_taxonomy');

// 注册"部门机构"分类法（第三级）
function register_department_taxonomy() {
    register_taxonomy('department', 'admin_check', array(
        'labels' => array(
            'name' => '部门机构',
            'singular_name' => '部门机构',
            'search_items' => '搜索部门机构',
            'all_items' => '所有部门机构',
            'edit_item' => '编辑部门机构',
            'update_item' => '更新部门机构',
            'add_new_item' => '添加新部门机构',
            'new_item_name' => '新部门机构名称',
            'menu_name' => '部门机构',
        ),
        'public' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'department'),
    ));
}
add_action('init', 'register_department_taxonomy');

// 在部门分类法添加页面添加区域类型选择器
function add_department_region_field() {
    $region_types = get_terms(array(
        'taxonomy' => 'region_type',
        'hide_empty' => false
    ));
    ?>
    <div class="form-field">
        <label for="department_region">区域类型</label>
        <select name="department_region" id="department_region">
            <option value="">请选择区域类型</option>
            <?php foreach ($region_types as $region): ?>
                <option value="<?php echo esc_attr($region->term_id); ?>">
                    <?php echo esc_html($region->name); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <p class="description">选择此部门所属的区域类型（政府部门或乡镇）</p>
    </div>
    <?php
}
add_action('department_add_form_fields', 'add_department_region_field');

// 在部门分类法编辑页面添加区域类型选择器
function edit_department_region_field($term) {
    $region_types = get_terms(array(
        'taxonomy' => 'region_type',
        'hide_empty' => false
    ));
    
    $selected_region = get_term_meta($term->term_id, 'department_region', true);
    ?>
    <tr class="form-field">
        <th scope="row" valign="top">
            <label for="department_region">区域类型</label>
        </th>
        <td>
            <select name="department_region" id="department_region">
                <option value="">请选择区域类型</option>
                <?php foreach ($region_types as $region): ?>
                    <option value="<?php echo esc_attr($region->term_id); ?>" 
                            <?php selected($selected_region, $region->term_id); ?>>
                        <?php echo esc_html($region->name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <p class="description">选择此部门所属的区域类型（政府部门或乡镇）</p>
        </td>
    </tr>
    <?php
}
add_action('department_edit_form_fields', 'edit_department_region_field');

// 保存部门区域关联
function save_department_region_field($term_id) {
    if (isset($_POST['department_region'])) {
        $region_id = sanitize_text_field($_POST['department_region']);
        if (!empty($region_id)) {
            update_term_meta($term_id, 'department_region', $region_id);
        } else {
            delete_term_meta($term_id, 'department_region');
        }
    }
}
add_action('created_department', 'save_department_region_field');
add_action('edited_department', 'save_department_region_field');

// 在部门列表页面显示区域类型
function add_department_region_column($columns) {
    $columns['region'] = '区域类型';
    return $columns;
}
add_filter('manage_edit-department_columns', 'add_department_region_column');

function display_department_region_column($content, $column_name, $term_id) {
    if ($column_name == 'region') {
        $region_id = get_term_meta($term_id, 'department_region', true);
        if ($region_id) {
            $region_term = get_term($region_id, 'region_type');
            if ($region_term && !is_wp_error($region_term)) {
                $content = $region_term->name;
            } else {
                $content = '<span style="color: #ccc;">未设置</span>';
            }
        } else {
            $content = '<span style="color: #ccc;">未设置</span>';
        }
    }
    return $content;
}
add_filter('manage_department_custom_column', 'display_department_region_column', 10, 3);

// 手动关联分类法
function associate_admin_check_taxonomies() {
    register_taxonomy_for_object_type('check_category', 'admin_check');
    register_taxonomy_for_object_type('region_type', 'admin_check');
    register_taxonomy_for_object_type('department', 'admin_check');
}
add_action('init', 'associate_admin_check_taxonomies', 99);

// 生产环境分类显示问题诊断和修复工具
// 1. 刷新重写规则函数 - 解决URL重写问题
function flush_admin_check_rewrite_rules() {
    // 重新注册自定义文章类型和分类法
    register_administrative_check_post_type();
    register_check_category_taxonomy();
    register_region_type_taxonomy();
    register_department_taxonomy();
    associate_admin_check_taxonomies();
    
    // 刷新重写规则
    flush_rewrite_rules();
    
    // 添加管理员通知
    add_action('admin_notices', function() {
        echo '<div class="notice notice-success is-dismissible"><p>行政检查系统重写规则已刷新！</p></div>';
    });
}

// 2. 检查分类法状态的调试函数
function debug_admin_check_taxonomies() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $debug_info = array();
    
    // 检查分类法是否注册
    $taxonomies = array('check_category', 'region_type', 'department');
    foreach ($taxonomies as $taxonomy) {
        $debug_info[$taxonomy] = array(
            'registered' => taxonomy_exists($taxonomy),
            'count' => wp_count_terms($taxonomy),
            'terms' => get_terms(array('taxonomy' => $taxonomy, 'hide_empty' => false))
        );
    }
    
    // 检查自定义文章类型
    $debug_info['admin_check_post_type'] = array(
        'registered' => post_type_exists('admin_check'),
        'count' => wp_count_posts('admin_check')
    );
    
    return $debug_info;
}

// 3. 初始化默认分类数据
function init_default_admin_check_data() {
    // 检查类别默认数据
    $default_categories = array(
        '行政检查主体' => array('description' => '行政检查主体相关信息', 'sort' => 1),
        '行政检查事项和依据' => array('description' => '行政检查事项和依据相关信息', 'sort' => 2),
        '行政检查频次上限' => array('description' => '行政检查频次上限相关信息', 'sort' => 3),
        '行政检查标准' => array('description' => '行政检查标准相关信息', 'sort' => 4),
        '检查计划' => array('description' => '检查计划相关信息', 'sort' => 5),
        '检查文书' => array('description' => '检查文书相关信息', 'sort' => 6)
    );
    
    foreach ($default_categories as $name => $data) {
        if (!term_exists($name, 'check_category')) {
            $term = wp_insert_term($name, 'check_category', array(
                'description' => $data['description'],
                'slug' => sanitize_title($name)
            ));
            
            if (!is_wp_error($term)) {
                update_term_meta($term['term_id'], 'sort', $data['sort']);
            }
        }
    }
    
    // 区域类型默认数据
    $default_regions = array(
        '乌鲁木齐县政府部门' => '县级政府各部门',
        '乌鲁木齐县乡（镇）' => '乡镇街道办事处'
    );
    
    foreach ($default_regions as $name => $description) {
        if (!term_exists($name, 'region_type')) {
            wp_insert_term($name, 'region_type', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
        }
    }
    
    add_action('admin_notices', function() {
        echo '<div class="notice notice-success is-dismissible"><p>默认分类数据已初始化！</p></div>';
    });
}

// 4. 管理员工具页面 - 添加到后台菜单
function add_admin_check_debug_menu() {
    add_submenu_page(
        'edit.php?post_type=admin_check',
        '系统诊断',
        '系统诊断',
        'manage_options',
        'admin-check-debug',
        'admin_check_debug_page'
    );
}
add_action('admin_menu', 'add_admin_check_debug_menu');

function admin_check_debug_page() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 处理操作
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'flush_rules':
                flush_admin_check_rewrite_rules();
                break;
            case 'init_data':
                init_default_admin_check_data();
                break;
        }
    }
    
    $debug_info = debug_admin_check_taxonomies();
    ?>
    <div class="wrap">
        <h1>行政检查系统诊断</h1>
        
        <div class="card">
            <h2>快速修复</h2>
            <form method="post">
                <p>
                    <input type="hidden" name="action" value="flush_rules">
                    <input type="submit" class="button button-primary" value="刷新重写规则">
                    <span class="description">解决URL重写和分类显示问题</span>
                </p>
            </form>
            
            <form method="post">
                <p>
                    <input type="hidden" name="action" value="init_data">
                    <input type="submit" class="button button-secondary" value="初始化默认数据">
                    <span class="description">创建默认的检查类别和区域类型</span>
                </p>
            </form>
        </div>
        
        <div class="card">
            <h2>系统状态</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>状态</th>
                        <th>数量</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>自定义文章类型 (admin_check)</td>
                        <td><?php echo $debug_info['admin_check_post_type']['registered'] ? '✅ 已注册' : '❌ 未注册'; ?></td>
                        <td><?php echo $debug_info['admin_check_post_type']['registered'] ? $debug_info['admin_check_post_type']['count']->publish : 0; ?></td>
                        <td>-</td>
                    </tr>
                    <?php foreach (array('check_category', 'region_type', 'department') as $taxonomy): ?>
                    <tr>
                        <td>分类法: <?php echo $taxonomy; ?></td>
                        <td><?php echo $debug_info[$taxonomy]['registered'] ? '✅ 已注册' : '❌ 未注册'; ?></td>
                        <td><?php echo $debug_info[$taxonomy]['count']; ?></td>
                        <td>
                            <?php if (!empty($debug_info[$taxonomy]['terms'])): ?>
                                <?php foreach ($debug_info[$taxonomy]['terms'] as $term): ?>
                                    <span class="dashicons dashicons-yes"></span> <?php echo $term->name; ?><br>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <span style="color: #d63638;">无数据</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>常见问题解决方案</h2>
            <ol>
                <li><strong>分类不显示</strong>：点击"刷新重写规则"按钮</li>
                <li><strong>没有默认分类</strong>：点击"初始化默认数据"按钮</li>
                <li><strong>前端页面404</strong>：到 设置 → 固定链接 保存一次</li>
                <li><strong>AJAX请求失败</strong>：检查主题是否正确加载，清除缓存</li>
            </ol>
        </div>
    </div>
    <?php
}

// Ajax处理函数 - 获取子级分类
function get_child_terms_ajax() {
    check_ajax_referer('admin_check_nonce', 'nonce');
    
    $parent_id = intval($_POST['parent_id']);
    $taxonomy = sanitize_text_field($_POST['taxonomy']);
    
    $terms = get_terms(array(
        'taxonomy' => $taxonomy,
        'parent' => $parent_id,
        'hide_empty' => false,
    ));
    
    $result = array();
    if (!is_wp_error($terms)) {
        foreach ($terms as $term) {
            $result[] = array(
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug
            );
        }
    }
    
    wp_send_json_success($result);
}
add_action('wp_ajax_get_child_terms', 'get_child_terms_ajax');
add_action('wp_ajax_nopriv_get_child_terms', 'get_child_terms_ajax');

// Ajax处理函数 - 获取检查项目
function get_admin_check_items_ajax() {
    check_ajax_referer('admin_check_nonce', 'nonce');
    
    $check_category = sanitize_text_field($_POST['check_category']);
    $region_type = sanitize_text_field($_POST['region_type']);
    $department = sanitize_text_field($_POST['department']);
    
    $args = array(
        'post_type' => 'admin_check',
        'posts_per_page' => -1,
        'tax_query' => array(
            'relation' => 'AND',
        ),
    );
    
    if (!empty($check_category)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'check_category',
            'field' => 'slug',
            'terms' => $check_category,
        );
    }
    
    if (!empty($region_type)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'region_type',
            'field' => 'slug',
            'terms' => $region_type,
        );
    }
    
    if (!empty($department)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'department',
            'field' => 'slug',
            'terms' => $department,
        );
    }
    
    $posts = get_posts($args);
    
    $result = array();
    foreach ($posts as $post) {
        $result[] = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'excerpt' => wp_trim_words($post->post_content, 20),
            'url' => get_permalink($post->ID),
        );
    }
    
    wp_send_json_success($result);
}
add_action('wp_ajax_get_admin_check_items', 'get_admin_check_items_ajax');
add_action('wp_ajax_nopriv_get_admin_check_items', 'get_admin_check_items_ajax');

// Ajax处理函数 - 根据区域类型获取部门
function get_departments_by_region_ajax() {
    // 验证nonce
    check_ajax_referer('admin_check_nonce', 'nonce');
    
    $region_name = sanitize_text_field($_POST['region']);
    
    // 获取所有部门
    $departments = get_terms(array(
        'taxonomy' => 'department',
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));
    
    $result = array();
    
    // 定义政府部门和乡镇列表
    $government_departments = array(
        '发展和改革委员会', '教育局', '民政局', '财政局', 
        '人力资源和社会保障局', '自然资源局', '生态环境局', 
        '住房和城乡建设局', '交通运输局', '水利局', 
        '农业农村局', '商务局', '文化体育广电和旅游局', 
        '卫生健康委员会', '应急管理局', '审计局', 
        '市场监督管理局', '统计局', '医疗保障局', '乡村振兴局'
    );
    
    foreach ($departments as $dept) {
        $department_id = get_term_meta($dept->term_id, 'department_id', true);
        $should_include = false;
        
        // 根据区域类型过滤部门
        if (strpos($region_name, '政府部门') !== false) {
            // 政府部门：包含在政府部门列表中，或者是"局"、"委员会"结尾的
            $should_include = in_array($dept->name, $government_departments) ||
                             strpos($dept->name, '局') !== false ||
                             strpos($dept->name, '委员会') !== false;
            
            // 排除明显的乡镇（必须是单独的乡镇名称，不是包含乡镇字样的政府部门）
            if ((strpos($dept->name, '乡') !== false || strpos($dept->name, '镇') !== false) &&
                !strpos($dept->name, '局') && !strpos($dept->name, '委员会')) {
                $should_include = false;
            }
        } else {
            // 乡镇：只包含单独的乡镇名称，不包含"局"或"委员会"
            $should_include = (strpos($dept->name, '乡') !== false || strpos($dept->name, '镇') !== false) &&
                             !strpos($dept->name, '局') && !strpos($dept->name, '委员会');
        }
        
        if ($should_include) {
            $result[] = array(
                'id' => $dept->term_id,
                'name' => $dept->name,
                'slug' => $dept->slug,
                'department_id' => $department_id ? $department_id : $dept->term_id
            );
        }
    }
    
    wp_send_json_success($result);
}

// 确保部门与区域类型正确关联的函数
function ensure_department_region_associations() {
    // 检查是否已经关联过
    if (get_option('department_region_associated')) {
        return;
    }
    
    // 获取区域类型
    $region_types = get_terms(array(
        'taxonomy' => 'region_type',
        'hide_empty' => false
    ));
    
    $gov_region = null;
    $town_region = null;
    
    foreach ($region_types as $region) {
        if (strpos($region->name, '政府部门') !== false) {
            $gov_region = $region;
        } elseif (strpos($region->name, '乡') !== false || strpos($region->name, '镇') !== false) {
            $town_region = $region;
        }
    }
    
    if (!$gov_region || !$town_region) {
        return;
    }
    
    // 政府部门列表
    $government_departments = array(
        '发展和改革委员会', '教育局', '民政局', '财政局', 
        '人力资源和社会保障局', '自然资源局', '生态环境局', 
        '住房和城乡建设局', '交通运输局', '水利局', 
        '农业农村局', '商务局', '文化体育广电和旅游局', 
        '卫生健康委员会', '应急管理局', '审计局', 
        '市场监督管理局', '统计局', '医疗保障局', '乡村振兴局'
    );
    
    // 获取所有部门
    $all_departments = get_terms(array(
        'taxonomy' => 'department',
        'hide_empty' => false
    ));
    
    foreach ($all_departments as $dept) {
        if (in_array($dept->name, $government_departments)) {
            // 关联到政府部门
            wp_set_object_terms($dept->term_id, $gov_region->term_id, 'region_type');
        } else {
            // 根据名称判断是否为乡镇
            if (strpos($dept->name, '乡') !== false || strpos($dept->name, '镇') !== false) {
                wp_set_object_terms($dept->term_id, $town_region->term_id, 'region_type');
            } else {
                // 其他未明确分类的部门默认归类为政府部门
                wp_set_object_terms($dept->term_id, $gov_region->term_id, 'region_type');
            }
        }
    }
    
    // 标记为已关联
    update_option('department_region_associated', true);
}
add_action('wp_ajax_get_departments_by_region', 'get_departments_by_region_ajax');
add_action('wp_ajax_nopriv_get_departments_by_region', 'get_departments_by_region_ajax');

// Ajax处理函数 - 根据筛选条件获取检查项目
function get_check_items_by_filters_ajax() {
    // 验证nonce
    if (!check_ajax_referer('admin_check_nonce', 'nonce', false)) {
        wp_send_json_error('安全验证失败');
        return;
    }
    
    $cate_id = isset($_POST['cate_id']) ? intval($_POST['cate_id']) : 0;
    $dept_id = isset($_POST['dept_id']) ? intval($_POST['dept_id']) : 0;
    $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : ''; // 兼容旧参数
    $region = isset($_POST['region']) ? sanitize_text_field($_POST['region']) : '';
    $department = isset($_POST['department']) ? sanitize_text_field($_POST['department']) : ''; // 兼容旧参数
    
    // URL解码分类参数（兼容旧参数）
    if (!empty($category)) {
        $category = urldecode($category);
    }
    
    // 每页数量（可选，默认全部）
    $per_page = -1;
    if (isset($_POST['per_page'])) {
        $per_page_val = intval($_POST['per_page']);
        if ($per_page_val > 0) {
            $per_page = $per_page_val;
        }
    }

    // 构建查询参数
    $args = array(
        'post_type' => 'admin_check',
        'posts_per_page' => $per_page,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    // 构建税务查询
    $tax_query = array();
    
    // 检查类别筛选
    $category_term = null;
    if (!empty($cate_id)) {
        // 通过cate_id查找分类
        $categories = get_terms(array(
            'taxonomy' => 'check_category',
            'hide_empty' => false,
            'meta_query' => array(
                array(
                    'key' => 'cate_id',
                    'value' => $cate_id,
                    'compare' => '='
                )
            )
        ));
        if (!empty($categories)) {
            $category_term = $categories[0];
            $tax_query[] = array(
                'taxonomy' => 'check_category',
                'field' => 'term_id',
                'terms' => $category_term->term_id,
            );
        }
    } elseif (!empty($category)) {
        // 兼容旧参数：先尝试按照slug查找
        $category_term = get_term_by('slug', $category, 'check_category');
        if (!$category_term) {
            // 如果按slug找不到，尝试按name查找
            $category_term = get_term_by('name', $category, 'check_category');
        }
        
        if ($category_term) {
            $tax_query[] = array(
                'taxonomy' => 'check_category',
                'field' => 'term_id',
                'terms' => $category_term->term_id,
            );
        }
    }
    
    // 部门机构筛选
    if (!empty($dept_id)) {
        // 通过dept_id查找部门
        $departments = get_terms(array(
            'taxonomy' => 'department',
            'hide_empty' => false,
            'meta_query' => array(
                array(
                    'key' => 'department_id',
                    'value' => $dept_id,
                    'compare' => '='
                )
            )
        ));
        if (!empty($departments)) {
            $dept_term = $departments[0];
            $tax_query[] = array(
                'taxonomy' => 'department',
                'field' => 'term_id',
                'terms' => $dept_term->term_id,
            );
        }
    } elseif (!empty($department)) {
        // 兼容旧参数：按名称查找
        $tax_query[] = array(
            'taxonomy' => 'department',
            'field' => 'name',
            'terms' => urldecode($department),
        );
    }
    
    // 应用税务查询
    if (!empty($tax_query)) {
        if (count($tax_query) > 1) {
            $tax_query['relation'] = 'AND';
        }
        $args['tax_query'] = $tax_query;
    }
    
    $posts = get_posts($args);
    
    $result = array();
    foreach ($posts as $post) {
        // 获取部门信息 - 尝试多个可能的分类法
        $department_name = '';
        
        // 获取部门信息
        $departments = wp_get_object_terms($post->ID, 'department');
        if (!empty($departments) && !is_wp_error($departments)) {
            $department_name = $departments[0]->name;
        }
        
        $result[] = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'url' => get_permalink($post->ID),
            'date' => get_the_date('Y-m-d', $post->ID),
            'department_name' => $department_name
        );
    }
    
    // 获取分类名称
    $category_name = '检查项目';
    if (isset($category_term) && $category_term && !is_wp_error($category_term)) {
        $category_name = $category_term->name;
    }
    
    wp_send_json_success(array(
        'items' => $result,
        'category_name' => $category_name
    ));
}

// 添加错误处理包装
function get_check_items_by_filters_ajax_wrapper() {
    try {
        get_check_items_by_filters_ajax();
    } catch (Exception $e) {
        error_log('AJAX Error: ' . $e->getMessage());
        wp_send_json_error('Server error: ' . $e->getMessage());
    }
}
add_action('wp_ajax_get_check_items_by_filters', 'get_check_items_by_filters_ajax_wrapper');
add_action('wp_ajax_nopriv_get_check_items_by_filters', 'get_check_items_by_filters_ajax_wrapper');

// 初始化检查类别的cate_id和sort字段
function initialize_check_categories() {
    // 检查是否已经初始化过
    if (get_option('check_categories_initialized')) {
        return;
    }
    
    // 预定义的分类配置
    $categories_config = array(
        '行政检查主体' => array('cate_id' => 1, 'sort' => 1),
        '行政检查事项和依据' => array('cate_id' => 2, 'sort' => 2),
        '行政检查频次上限' => array('cate_id' => 3, 'sort' => 3),
        '行政检查标准' => array('cate_id' => 4, 'sort' => 4),
        '检查计划' => array('cate_id' => 5, 'sort' => 5),
        '检查文书' => array('cate_id' => 6, 'sort' => 6)
    );
    
    // 获取所有检查类别
    $categories = get_terms(array(
        'taxonomy' => 'check_category',
        'hide_empty' => false
    ));
    
    foreach ($categories as $category) {
        if (isset($categories_config[$category->name])) {
            $config = $categories_config[$category->name];
            
            // 设置 cate_id
            update_term_meta($category->term_id, 'cate_id', $config['cate_id']);
            
            // 设置 sort
            update_term_meta($category->term_id, 'sort', $config['sort']);
            
            error_log('初始化分类: ' . $category->name . ' - cate_id: ' . $config['cate_id'] . ', sort: ' . $config['sort']);
        }
    }
    
    // 标记为已初始化
    update_option('check_categories_initialized', true);
}

// 在主题激活时运行初始化
add_action('after_setup_theme', 'initialize_check_categories');
// 也在WordPress初始化时运行一次，确保数据正确设置
add_action('init', 'initialize_check_categories');

// 初始化部门机构的department_id字段
function initialize_department_institutions() {
    // 检查是否已经初始化过
    if (get_option('department_institutions_initialized')) {
        return;
    }
    
    // 获取所有部门机构
    $departments = get_terms(array(
        'taxonomy' => 'department',
        'hide_empty' => false
    ));
    
    $department_id = 1;
    foreach ($departments as $department) {
        // 设置 department_id
        update_term_meta($department->term_id, 'department_id', $department_id);
        
        error_log('初始化部门: ' . $department->name . ' - department_id: ' . $department_id);
        $department_id++;
    }
    
    // 标记为已初始化
    update_option('department_institutions_initialized', true);
}

// 在主题激活时运行部门初始化
add_action('after_setup_theme', 'initialize_department_institutions');





// 迁移现有部门到新的term meta系统
function migrate_existing_departments_to_term_meta() {
    // 检查是否已经迁移过
    if (get_option('departments_migrated_to_term_meta')) {
        return;
    }
    
    // 获取区域类型
    $region_types = get_terms(array(
        'taxonomy' => 'region_type',
        'hide_empty' => false
    ));
    
    $gov_region = null;
    $town_region = null;
    
    foreach ($region_types as $region) {
        if (strpos($region->name, '政府部门') !== false) {
            $gov_region = $region;
        } elseif (strpos($region->name, '乡') !== false || strpos($region->name, '镇') !== false) {
            $town_region = $region;
        }
    }
    
    if (!$gov_region || !$town_region) {
        return;
    }
    
    // 政府部门名单
    $government_departments = array(
        '发展和改革委员会', '教育局', '民政局', '财政局', 
        '人力资源和社会保障局', '自然资源局', '生态环境局', 
        '住房和城乡建设局', '交通运输局', '水利局', 
        '农业农村局', '商务局', '文化体育广电和旅游局', 
        '卫生健康委员会', '应急管理局', '审计局', 
        '市场监督管理局', '统计局', '医疗保障局', '乡村振兴局'
    );
    
    // 获取所有部门
    $departments = get_terms(array(
        'taxonomy' => 'department',
        'hide_empty' => false
    ));
    
    foreach ($departments as $dept) {
        // 检查是否已经设置了区域关联
        $existing_region = get_term_meta($dept->term_id, 'department_region', true);
        if (!empty($existing_region)) {
            continue; // 已经设置过，跳过
        }
        
        // 根据部门名称判断区域类型
        if (in_array($dept->name, $government_departments)) {
            // 政府部门
            update_term_meta($dept->term_id, 'department_region', $gov_region->term_id);
        } elseif (strpos($dept->name, '乡') !== false || strpos($dept->name, '镇') !== false) {
            // 乡镇
            update_term_meta($dept->term_id, 'department_region', $town_region->term_id);
        } else {
            // 其他部门默认归类为政府部门
            update_term_meta($dept->term_id, 'department_region', $gov_region->term_id);
        }
    }
    
    // 标记为已迁移
    update_option('departments_migrated_to_term_meta', true);
}

// 数据初始化函数（只运行一次）
function init_admin_check_data() {
    // 检查是否已经初始化过
    if (get_option('admin_check_data_initialized')) {
        return;
    }
    
    // 第一级：检查类别（6个固定类型）
    $check_categories = array(
        '行政检查主体' => '负责实施行政检查的部门和机构',
        '行政检查事项和依据' => '检查事项的具体内容和法律依据',
        '行政检查频次上限' => '各类检查的频次限制规定',
        '行政检查标准' => '检查工作的标准和规范要求',
        '检查计划' => '年度、季度等检查工作计划安排',
        '检查文书' => '检查过程中使用的各类文书模板'
    );

    // 第二级：区域类型
    $region_types = array(
        '乌鲁木齐县政府部门' => '乌鲁木齐县各政府职能部门',
        '乌鲁木齐县乡（镇）' => '乌鲁木齐县下属各乡镇'
    );

    // 第三级：部门机构（政府部门）
    $government_departments = array(
        '财政局' => '负责财政预算管理和资金监督工作',
        '城市管理行政执法局' => '负责城市管理和行政执法工作',
        '公安局' => '负责社会治安和公共安全管理',
        '保密局' => '负责保密工作和信息安全管理',
        '教育局' => '负责教育事业发展和管理工作',
        '科学技术局' => '负责科技创新和技术推广工作',
        '林业和草原局' => '负责林业资源和草原生态保护',
        '民政局' => '负责民政事务和社会救助工作',
        '农村农业局' => '负责农业发展和农村工作管理',
        '人力资源和社会保障局' => '负责人事管理和社会保障工作',
        '市场监督管理局' => '负责市场监管和质量安全工作',
        '统计局' => '负责统计调查和数据分析工作',
        '文化体育广电和旅游局' => '负责文化体育广电旅游事业管理',
        '医疗保障局' => '负责医疗保障制度和基金管理',
        '应急管理局' => '负责应急管理和安全生产工作',
        '县委办公室' => '负责县委日常事务和综合协调',
        '发展和改革委员会' => '负责经济发展规划和改革工作',
        '自然资源局' => '负责自然资源管理和国土规划',
        '建设局' => '负责城乡建设和工程管理工作',
        '交通运输局' => '负责交通运输建设和管理工作', 
        '水务局' => '负责水利工程建设和水资源管理',
        '卫生健康委员会' => '负责卫生健康事业发展和管理',
        '审计局' => '负责财政资金和经济活动审计',
        '互联网办公室' => '负责网络安全和信息化建设'
    );

    // 第三级：乡镇
    $townships = array(
        '水西沟镇' => '乌鲁木齐县水西沟镇人民政府',
        '板房沟镇' => '乌鲁木齐县板房沟镇人民政府',
        '甘沟乡' => '乌鲁木齐县甘沟乡人民政府',
        '萨尔达坂乡' => '乌鲁木齐县萨尔达坂乡人民政府',
        '托里乡' => '乌鲁木齐县托里乡人民政府'
    );

    // 1. 添加检查类别
    foreach ($check_categories as $name => $description) {
        if (!term_exists($name, 'check_category')) {
            wp_insert_term($name, 'check_category', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
        }
    }

    // 2. 添加区域类型
    foreach ($region_types as $name => $description) {
        if (!term_exists($name, 'region_type')) {
            wp_insert_term($name, 'region_type', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
        }
    }

    // 获取区域类型，用于关联
    $gov_region = get_term_by('slug', sanitize_title('乌鲁木齐县政府部门'), 'region_type');
    $town_region = get_term_by('slug', sanitize_title('乌鲁木齐县乡（镇）'), 'region_type');

    // 3. 添加政府部门
    foreach ($government_departments as $name => $description) {
        if (!term_exists($name, 'department')) {
            $result = wp_insert_term($name, 'department', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
            
            // 关联到政府部门区域类型
            if (!is_wp_error($result) && $gov_region) {
                wp_set_object_terms($result['term_id'], $gov_region->term_id, 'region_type');
            }
        }
    }

    // 4. 添加乡镇
    foreach ($townships as $name => $description) {
        if (!term_exists($name, 'department')) {
            $result = wp_insert_term($name, 'department', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
            
            // 关联到乡镇区域类型
            if (!is_wp_error($result) && $town_region) {
                wp_set_object_terms($result['term_id'], $town_region->term_id, 'region_type');
            }
        }
    }

    // 刷新重写规则
    flush_rewrite_rules();
    
    // 标记为已初始化
    update_option('admin_check_data_initialized', true);
    
    // 初始化后立即运行检查类别初始化
    initialize_check_categories();
    initialize_department_institutions();
}

// 在主题激活时运行初始化
add_action('after_setup_theme', 'init_admin_check_data');
// 也在WordPress完全初始化后运行一次
add_action('init', 'init_admin_check_data');

// 强制重新初始化所有数据的函数
function force_reinit_admin_check_data() {
    // 清除所有初始化标记
    delete_option('admin_check_data_initialized');
    delete_option('check_categories_initialized');
    delete_option('department_institutions_initialized');

    // 重新运行初始化
    init_admin_check_data();
    initialize_check_categories();
    initialize_department_institutions();

    // 刷新重写规则
    flush_rewrite_rules();

    return true;
}

// 强制创建检查类别的函数
function force_create_check_categories() {
    // 确保分类法已注册
    register_check_category_taxonomy();

    $categories_to_create = array(
        '行政检查主体' => array('description' => '负责实施行政检查的部门和机构', 'sort' => 1, 'cate_id' => 1),
        '行政检查事项和依据' => array('description' => '检查事项的具体内容和法律依据', 'sort' => 2, 'cate_id' => 2),
        '行政检查频次上限' => array('description' => '各类检查的频次限制规定', 'sort' => 3, 'cate_id' => 3),
        '行政检查标准' => array('description' => '检查工作的标准和规范要求', 'sort' => 4, 'cate_id' => 4),
        '检查计划' => array('description' => '年度、季度等检查工作计划安排', 'sort' => 5, 'cate_id' => 5),
        '检查文书' => array('description' => '检查过程中使用的各类文书模板', 'sort' => 6, 'cate_id' => 6)
    );

    $created_count = 0;
    foreach ($categories_to_create as $name => $data) {
        // 检查是否已存在
        if (!term_exists($name, 'check_category')) {
            $term = wp_insert_term($name, 'check_category', array(
                'description' => $data['description'],
                'slug' => sanitize_title($name)
            ));

            if (!is_wp_error($term)) {
                update_term_meta($term['term_id'], 'sort', $data['sort']);
                update_term_meta($term['term_id'], 'cate_id', $data['cate_id']);
                $created_count++;
            }
        }
    }

    return $created_count;
}

// 临时重置和修复教育局关联（访问一次后自动删除）
function temp_fix_education_department() {
    // 检查是否已经修复过
    if (get_option('temp_education_fixed')) {
        return;
    }
    
    // 重置初始化标记，重新运行数据初始化
    delete_option('admin_check_data_initialized');
    delete_option('department_region_associated');
    
    // 运行初始化
    init_admin_check_data();
    
    // 确保教育局关联到政府部门
    $education_dept = get_term_by('name', '教育局', 'department');
    $gov_region = get_term_by('slug', sanitize_title('乌鲁木齐县政府部门'), 'region_type');
    
    if ($education_dept && $gov_region) {
        wp_set_object_terms($education_dept->term_id, $gov_region->term_id, 'region_type');
    }
    
    // 标记为已修复
    update_option('temp_education_fixed', true);
}
add_action('init', 'temp_fix_education_department');

function custom_rewrite_admin_check_nav() {
    add_rewrite_rule(
        '^openscat/wlmqxsqxzjcgszl/?$',
        'index.php?pagename=admin-check-nav',
        'top'
    );
}
add_action('init', 'custom_rewrite_admin_check_nav');

?>









