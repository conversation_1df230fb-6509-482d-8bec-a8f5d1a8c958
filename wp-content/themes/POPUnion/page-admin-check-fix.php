<?php
/*
Template Name: 行政检查系统修复工具
*/

// 检查管理员权限
if (!current_user_can('manage_options')) {
    wp_die('您没有权限访问此页面');
}

// 处理修复操作
if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'flush_rules':
            flush_admin_check_rewrite_rules();
            $message = '重写规则已刷新！';
            break;
        case 'init_data':
            // 强制重新初始化数据
            delete_option('admin_check_data_initialized');
            delete_option('check_categories_initialized');
            delete_option('department_institutions_initialized');
            init_admin_check_data();
            initialize_check_categories();
            initialize_department_institutions();
            $message = '数据已重新初始化！';
            break;
        case 'create_categories':
            create_missing_check_categories();
            $message = '检查类别已创建！';
            break;
    }
}

// 获取诊断信息
function get_admin_check_diagnosis() {
    $diagnosis = array();
    
    // 检查自定义文章类型
    $diagnosis['post_type'] = array(
        'registered' => post_type_exists('admin_check'),
        'count' => wp_count_posts('admin_check')
    );
    
    // 检查分类法
    $taxonomies = array('check_category', 'region_type', 'department');
    foreach ($taxonomies as $taxonomy) {
        $terms = get_terms(array('taxonomy' => $taxonomy, 'hide_empty' => false));
        $diagnosis[$taxonomy] = array(
            'registered' => taxonomy_exists($taxonomy),
            'count' => count($terms),
            'terms' => $terms
        );
    }
    
    // 检查初始化状态
    $diagnosis['init_status'] = array(
        'admin_check_data_initialized' => get_option('admin_check_data_initialized'),
        'check_categories_initialized' => get_option('check_categories_initialized'),
        'department_institutions_initialized' => get_option('department_institutions_initialized')
    );
    
    return $diagnosis;
}

// 创建缺失的检查类别
function create_missing_check_categories() {
    $default_categories = array(
        '行政检查主体' => array('description' => '负责实施行政检查的部门和机构', 'sort' => 1, 'cate_id' => 1),
        '行政检查事项和依据' => array('description' => '检查事项的具体内容和法律依据', 'sort' => 2, 'cate_id' => 2),
        '行政检查频次上限' => array('description' => '各类检查的频次限制规定', 'sort' => 3, 'cate_id' => 3),
        '行政检查标准' => array('description' => '检查工作的标准和规范要求', 'sort' => 4, 'cate_id' => 4),
        '检查计划' => array('description' => '年度、季度等检查工作计划安排', 'sort' => 5, 'cate_id' => 5),
        '检查文书' => array('description' => '检查过程中使用的各类文书模板', 'sort' => 6, 'cate_id' => 6)
    );
    
    foreach ($default_categories as $name => $data) {
        if (!term_exists($name, 'check_category')) {
            $term = wp_insert_term($name, 'check_category', array(
                'description' => $data['description'],
                'slug' => sanitize_title($name)
            ));
            
            if (!is_wp_error($term)) {
                update_term_meta($term['term_id'], 'sort', $data['sort']);
                update_term_meta($term['term_id'], 'cate_id', $data['cate_id']);
            }
        }
    }
}

$diagnosis = get_admin_check_diagnosis();
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>行政检查系统修复工具</title>
    <?php wp_head(); ?>
    <style>
        .fix-tool-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .message { 
            background: #d4edda; 
            color: #155724; 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
    </style>
</head>
<body>

<div class="fix-tool-container">
    <h1>🔧 行政检查系统修复工具</h1>
    
    <?php if (isset($message)): ?>
        <div class="message"><?php echo esc_html($message); ?></div>
    <?php endif; ?>
    
    <!-- 系统状态诊断 -->
    <div class="card">
        <h2>📊 系统状态诊断</h2>
        
        <h3>自定义文章类型状态</h3>
        <table>
            <tr>
                <th>项目</th>
                <th>状态</th>
                <th>数量</th>
            </tr>
            <tr>
                <td>admin_check 文章类型</td>
                <td class="<?php echo $diagnosis['post_type']['registered'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $diagnosis['post_type']['registered'] ? '✅ 已注册' : '❌ 未注册'; ?>
                </td>
                <td><?php echo $diagnosis['post_type']['registered'] ? $diagnosis['post_type']['count']->publish : 0; ?></td>
            </tr>
        </table>
        
        <h3>分类法状态</h3>
        <table>
            <tr>
                <th>分类法</th>
                <th>注册状态</th>
                <th>分类数量</th>
                <th>详情</th>
            </tr>
            <?php foreach (array('check_category', 'region_type', 'department') as $taxonomy): ?>
            <tr>
                <td><?php echo $taxonomy; ?></td>
                <td class="<?php echo $diagnosis[$taxonomy]['registered'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $diagnosis[$taxonomy]['registered'] ? '✅ 已注册' : '❌ 未注册'; ?>
                </td>
                <td><?php echo $diagnosis[$taxonomy]['count']; ?></td>
                <td>
                    <?php if (!empty($diagnosis[$taxonomy]['terms'])): ?>
                        <?php foreach ($diagnosis[$taxonomy]['terms'] as $term): ?>
                            <span style="background: #e9ecef; padding: 2px 6px; margin: 2px; border-radius: 3px; font-size: 12px;">
                                <?php echo esc_html($term->name); ?>
                            </span>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <span class="status-warning">无数据</span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <h3>初始化状态</h3>
        <table>
            <tr>
                <th>初始化项目</th>
                <th>状态</th>
            </tr>
            <?php foreach ($diagnosis['init_status'] as $key => $status): ?>
            <tr>
                <td><?php echo $key; ?></td>
                <td class="<?php echo $status ? 'status-good' : 'status-warning'; ?>">
                    <?php echo $status ? '✅ 已完成' : '⚠️ 未完成'; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <!-- 修复操作 -->
    <div class="card">
        <h2>🛠️ 修复操作</h2>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="flush_rules">
            <button type="submit" class="btn">刷新重写规则</button>
        </form>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="create_categories">
            <button type="submit" class="btn btn-success">创建检查类别</button>
        </form>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="init_data">
            <button type="submit" class="btn btn-danger" onclick="return confirm('这将重新初始化所有数据，确定继续吗？')">
                强制重新初始化数据
            </button>
        </form>
    </div>
    
    <!-- 检查类别详情 -->
    <?php if (!empty($diagnosis['check_category']['terms'])): ?>
    <div class="card">
        <h2>📋 检查类别详情</h2>
        <table>
            <tr>
                <th>名称</th>
                <th>描述</th>
                <th>排序</th>
                <th>分类ID</th>
                <th>Slug</th>
            </tr>
            <?php foreach ($diagnosis['check_category']['terms'] as $term): ?>
            <tr>
                <td><?php echo esc_html($term->name); ?></td>
                <td><?php echo esc_html($term->description); ?></td>
                <td><?php echo get_term_meta($term->term_id, 'sort', true); ?></td>
                <td><?php echo get_term_meta($term->term_id, 'cate_id', true); ?></td>
                <td><?php echo esc_html($term->slug); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    <?php endif; ?>
    
    <!-- 使用说明 -->
    <div class="card">
        <h2>📖 使用说明</h2>
        <ol>
            <li><strong>刷新重写规则</strong>：解决URL重写和分类法注册问题</li>
            <li><strong>创建检查类别</strong>：创建6个标准的行政检查类别</li>
            <li><strong>强制重新初始化数据</strong>：清除所有初始化标记并重新创建所有数据</li>
        </ol>
        
        <h3>预期结果</h3>
        <p>修复完成后，应该看到：</p>
        <ul>
            <li>✅ check_category 分类法包含6个类别</li>
            <li>✅ region_type 分类法包含2个区域类型</li>
            <li>✅ department 分类法包含相关部门</li>
            <li>✅ 所有初始化状态为"已完成"</li>
        </ul>
    </div>
</div>

<?php wp_footer(); ?>
</body>
</html>
