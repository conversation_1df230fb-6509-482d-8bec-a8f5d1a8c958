<?php
/*
Template Name: 调试检查类别
*/

// 检查管理员权限
if (!current_user_can('manage_options')) {
    wp_die('您没有权限访问此页面');
}

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <title>调试检查类别</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        pre { background: #f4f4f4; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<h1>🔍 检查类别调试信息</h1>

<div class="debug-section">
    <h2>1. 分类法注册状态</h2>
    <?php
    $taxonomy_exists = taxonomy_exists('check_category');
    echo '<p>check_category 分类法是否存在: ';
    echo $taxonomy_exists ? '<span class="success">✅ 是</span>' : '<span class="error">❌ 否</span>';
    echo '</p>';
    
    if ($taxonomy_exists) {
        $taxonomy_object = get_taxonomy('check_category');
        echo '<p>分类法对象类型: ' . implode(', ', $taxonomy_object->object_type) . '</p>';
        echo '<p>分类法是否公开: ' . ($taxonomy_object->public ? '是' : '否') . '</p>';
    }
    ?>
</div>

<div class="debug-section">
    <h2>2. 获取分类数据测试</h2>
    <?php
    // 测试1: 基本获取
    echo '<h3>测试1: 基本获取所有分类</h3>';
    $categories_basic = get_terms(array(
        'taxonomy' => 'check_category',
        'hide_empty' => false
    ));
    
    if (is_wp_error($categories_basic)) {
        echo '<p class="error">错误: ' . $categories_basic->get_error_message() . '</p>';
    } else {
        echo '<p>找到 ' . count($categories_basic) . ' 个分类</p>';
        if (!empty($categories_basic)) {
            echo '<table>';
            echo '<tr><th>ID</th><th>名称</th><th>Slug</th><th>描述</th><th>Count</th></tr>';
            foreach ($categories_basic as $cat) {
                echo '<tr>';
                echo '<td>' . $cat->term_id . '</td>';
                echo '<td>' . esc_html($cat->name) . '</td>';
                echo '<td>' . esc_html($cat->slug) . '</td>';
                echo '<td>' . esc_html($cat->description) . '</td>';
                echo '<td>' . $cat->count . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
    
    // 测试2: 带排序的获取（和页面模板相同）
    echo '<h3>测试2: 带排序的获取（页面模板使用的方法）</h3>';
    $categories_sorted = get_terms(array(
        'taxonomy' => 'check_category',
        'hide_empty' => false,
        'meta_key' => 'sort',
        'orderby' => 'meta_value_num',
        'order' => 'ASC'
    ));
    
    if (is_wp_error($categories_sorted)) {
        echo '<p class="error">错误: ' . $categories_sorted->get_error_message() . '</p>';
    } else {
        echo '<p>找到 ' . count($categories_sorted) . ' 个分类</p>';
        if (!empty($categories_sorted)) {
            echo '<table>';
            echo '<tr><th>ID</th><th>名称</th><th>Sort Meta</th><th>Cate_ID Meta</th></tr>';
            foreach ($categories_sorted as $cat) {
                $sort_meta = get_term_meta($cat->term_id, 'sort', true);
                $cate_id_meta = get_term_meta($cat->term_id, 'cate_id', true);
                echo '<tr>';
                echo '<td>' . $cat->term_id . '</td>';
                echo '<td>' . esc_html($cat->name) . '</td>';
                echo '<td>' . ($sort_meta ? $sort_meta : '<span class="warning">未设置</span>') . '</td>';
                echo '<td>' . ($cate_id_meta ? $cate_id_meta : '<span class="warning">未设置</span>') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
    ?>
</div>

<div class="debug-section">
    <h2>3. 数据库直接查询</h2>
    <?php
    global $wpdb;
    
    // 查询分类法表
    $taxonomy_terms = $wpdb->get_results("
        SELECT t.term_id, t.name, t.slug, tt.taxonomy, tt.count 
        FROM {$wpdb->terms} t 
        JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id 
        WHERE tt.taxonomy = 'check_category'
        ORDER BY t.name
    ");
    
    echo '<h3>数据库中的 check_category 分类:</h3>';
    if (empty($taxonomy_terms)) {
        echo '<p class="error">❌ 数据库中没有找到 check_category 分类数据</p>';
    } else {
        echo '<p class="success">✅ 数据库中找到 ' . count($taxonomy_terms) . ' 个分类</p>';
        echo '<table>';
        echo '<tr><th>Term ID</th><th>名称</th><th>Slug</th><th>Count</th></tr>';
        foreach ($taxonomy_terms as $term) {
            echo '<tr>';
            echo '<td>' . $term->term_id . '</td>';
            echo '<td>' . esc_html($term->name) . '</td>';
            echo '<td>' . esc_html($term->slug) . '</td>';
            echo '<td>' . $term->count . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    // 查询元数据
    if (!empty($taxonomy_terms)) {
        echo '<h3>分类元数据:</h3>';
        echo '<table>';
        echo '<tr><th>Term ID</th><th>Meta Key</th><th>Meta Value</th></tr>';
        foreach ($taxonomy_terms as $term) {
            $meta_data = $wpdb->get_results($wpdb->prepare("
                SELECT meta_key, meta_value 
                FROM {$wpdb->termmeta} 
                WHERE term_id = %d
            ", $term->term_id));
            
            if (empty($meta_data)) {
                echo '<tr><td>' . $term->term_id . '</td><td colspan="2"><span class="warning">无元数据</span></td></tr>';
            } else {
                foreach ($meta_data as $meta) {
                    echo '<tr>';
                    echo '<td>' . $term->term_id . '</td>';
                    echo '<td>' . esc_html($meta->meta_key) . '</td>';
                    echo '<td>' . esc_html($meta->meta_value) . '</td>';
                    echo '</tr>';
                }
            }
        }
        echo '</table>';
    }
    ?>
</div>

<div class="debug-section">
    <h2>4. 初始化状态检查</h2>
    <?php
    $init_flags = array(
        'admin_check_data_initialized' => get_option('admin_check_data_initialized'),
        'check_categories_initialized' => get_option('check_categories_initialized'),
        'department_institutions_initialized' => get_option('department_institutions_initialized')
    );
    
    echo '<table>';
    echo '<tr><th>初始化标记</th><th>状态</th></tr>';
    foreach ($init_flags as $flag => $status) {
        echo '<tr>';
        echo '<td>' . $flag . '</td>';
        echo '<td>' . ($status ? '<span class="success">✅ 已完成</span>' : '<span class="warning">⚠️ 未完成</span>') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    ?>
</div>

<div class="debug-section">
    <h2>5. 快速修复操作</h2>
    
    <?php if (isset($_POST['action'])): ?>
        <?php
        switch ($_POST['action']) {
            case 'create_categories':
                echo '<h3>创建分类结果:</h3>';
                $categories_to_create = array(
                    '行政检查主体' => array('description' => '负责实施行政检查的部门和机构', 'sort' => 1, 'cate_id' => 1),
                    '行政检查事项和依据' => array('description' => '检查事项的具体内容和法律依据', 'sort' => 2, 'cate_id' => 2),
                    '行政检查频次上限' => array('description' => '各类检查的频次限制规定', 'sort' => 3, 'cate_id' => 3),
                    '行政检查标准' => array('description' => '检查工作的标准和规范要求', 'sort' => 4, 'cate_id' => 4),
                    '检查计划' => array('description' => '年度、季度等检查工作计划安排', 'sort' => 5, 'cate_id' => 5),
                    '检查文书' => array('description' => '检查过程中使用的各类文书模板', 'sort' => 6, 'cate_id' => 6)
                );
                
                foreach ($categories_to_create as $name => $data) {
                    if (!term_exists($name, 'check_category')) {
                        $term = wp_insert_term($name, 'check_category', array(
                            'description' => $data['description'],
                            'slug' => sanitize_title($name)
                        ));
                        
                        if (!is_wp_error($term)) {
                            update_term_meta($term['term_id'], 'sort', $data['sort']);
                            update_term_meta($term['term_id'], 'cate_id', $data['cate_id']);
                            echo '<p class="success">✅ 创建分类: ' . $name . ' (ID: ' . $term['term_id'] . ')</p>';
                        } else {
                            echo '<p class="error">❌ 创建分类失败: ' . $name . ' - ' . $term->get_error_message() . '</p>';
                        }
                    } else {
                        echo '<p class="warning">⚠️ 分类已存在: ' . $name . '</p>';
                    }
                }
                break;
                
            case 'flush_rules':
                flush_rewrite_rules();
                echo '<p class="success">✅ 重写规则已刷新</p>';
                break;
        }
        echo '<p><a href="' . $_SERVER['REQUEST_URI'] . '">刷新页面查看最新状态</a></p>';
        ?>
    <?php endif; ?>
    
    <form method="post" style="margin: 10px 0;">
        <input type="hidden" name="action" value="create_categories">
        <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
            创建6个标准检查类别
        </button>
    </form>
    
    <form method="post" style="margin: 10px 0;">
        <input type="hidden" name="action" value="flush_rules">
        <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
            刷新重写规则
        </button>
    </form>
</div>

<div class="debug-section">
    <h2>6. 页面模板代码测试</h2>
    <p>模拟页面模板中的代码执行:</p>
    <?php
    echo '<pre>';
    echo "// 页面模板中的代码:\n";
    echo '$check_categories = get_terms(array(' . "\n";
    echo '    \'taxonomy\' => \'check_category\',' . "\n";
    echo '    \'hide_empty\' => false,' . "\n";
    echo '    \'meta_key\' => \'sort\',' . "\n";
    echo '    \'orderby\' => \'meta_value_num\',' . "\n";
    echo '    \'order\' => \'ASC\'' . "\n";
    echo '));' . "\n\n";
    
    $test_categories = get_terms(array(
        'taxonomy' => 'check_category',
        'hide_empty' => false,
        'meta_key' => 'sort',
        'orderby' => 'meta_value_num',
        'order' => 'ASC'
    ));
    
    echo '// 执行结果:' . "\n";
    if (is_wp_error($test_categories)) {
        echo 'ERROR: ' . $test_categories->get_error_message() . "\n";
    } else {
        echo 'SUCCESS: 找到 ' . count($test_categories) . ' 个分类' . "\n";
        if (!empty($test_categories)) {
            echo 'foreach ($check_categories as $index => $category) {' . "\n";
            foreach ($test_categories as $index => $category) {
                echo '    // $index = ' . $index . ', $category->name = "' . $category->name . '"' . "\n";
            }
            echo '}' . "\n";
        } else {
            echo '// foreach 循环不会执行，因为数组为空' . "\n";
        }
    }
    echo '</pre>';
    ?>
</div>

</body>
</html>
