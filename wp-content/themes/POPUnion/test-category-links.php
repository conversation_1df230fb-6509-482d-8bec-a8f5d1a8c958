<?php
/*
Template Name: 测试分类链接
*/

// 检查管理员权限
if (!current_user_can('manage_options')) {
    wp_die('您没有权限访问此页面');
}
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>测试分类链接</title>
    <?php wp_head(); ?>
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .link-test {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .link-test.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .link-test.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-link {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .test-link:hover {
            background: #005a87;
            color: white;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>

<div class="test-container">
    <h1>🔗 分类链接测试</h1>
    
    <div class="card">
        <h2>📋 检查类别链接测试</h2>
        
        <?php
        $check_categories = get_terms(array(
            'taxonomy' => 'check_category',
            'hide_empty' => false,
            'meta_key' => 'sort',
            'orderby' => 'meta_value_num',
            'order' => 'ASC'
        ));
        
        if (empty($check_categories)) {
            echo '<p class="status-bad">❌ 没有找到检查类别</p>';
        } else {
            echo '<p class="status-good">✅ 找到 ' . count($check_categories) . ' 个检查类别</p>';
            
            foreach ($check_categories as $category) {
                $link = get_term_link($category);
                $is_error = is_wp_error($link);
                
                echo '<div class="link-test ' . ($is_error ? 'error' : 'success') . '">';
                echo '<h3>' . esc_html($category->name) . '</h3>';
                echo '<p><strong>分类ID:</strong> ' . $category->term_id . '</p>';
                echo '<p><strong>Slug:</strong> ' . esc_html($category->slug) . '</p>';
                echo '<p><strong>描述:</strong> ' . esc_html($category->description) . '</p>';
                
                if ($is_error) {
                    echo '<p class="status-bad"><strong>链接错误:</strong> ' . $link->get_error_message() . '</p>';
                } else {
                    echo '<p><strong>链接:</strong> <code>' . esc_html($link) . '</code></p>';
                    echo '<a href="' . esc_url($link) . '" class="test-link" target="_blank">测试链接</a>';
                }
                
                // 检查元数据
                $sort = get_term_meta($category->term_id, 'sort', true);
                $cate_id = get_term_meta($category->term_id, 'cate_id', true);
                echo '<p><strong>排序:</strong> ' . ($sort ? $sort : '未设置') . '</p>';
                echo '<p><strong>分类ID:</strong> ' . ($cate_id ? $cate_id : '未设置') . '</p>';
                
                echo '</div>';
            }
        }
        ?>
    </div>
    
    <div class="card">
        <h2>🔧 分类法状态检查</h2>
        
        <?php
        $taxonomies = array(
            'check_category' => '检查类别',
            'region_type' => '区域类型',
            'department' => '部门机构'
        );
        
        foreach ($taxonomies as $taxonomy => $label) {
            $exists = taxonomy_exists($taxonomy);
            $tax_object = get_taxonomy($taxonomy);
            
            echo '<div class="link-test ' . ($exists ? 'success' : 'error') . '">';
            echo '<h3>' . $label . ' (' . $taxonomy . ')</h3>';
            echo '<p><strong>注册状态:</strong> ' . ($exists ? '<span class="status-good">✅ 已注册</span>' : '<span class="status-bad">❌ 未注册</span>') . '</p>';
            
            if ($exists && $tax_object) {
                echo '<p><strong>公开访问:</strong> ' . ($tax_object->public ? '<span class="status-good">✅ 是</span>' : '<span class="status-warning">⚠️ 否</span>') . '</p>';
                echo '<p><strong>重写规则:</strong> ' . (isset($tax_object->rewrite['slug']) ? '<code>' . $tax_object->rewrite['slug'] . '</code>' : '<span class="status-warning">未设置</span>') . '</p>';
                echo '<p><strong>显示UI:</strong> ' . ($tax_object->show_ui ? '✅ 是' : '❌ 否') . '</p>';
                echo '<p><strong>前端查询:</strong> ' . ($tax_object->publicly_queryable ? '✅ 是' : '❌ 否') . '</p>';
            }
            
            echo '</div>';
        }
        ?>
    </div>
    
    <div class="card">
        <h2>🌐 重写规则检查</h2>
        
        <?php
        global $wp_rewrite;
        $rewrite_rules = $wp_rewrite->wp_rewrite_rules();
        
        echo '<h3>相关重写规则:</h3>';
        $found_rules = false;
        
        if (!empty($rewrite_rules)) {
            foreach ($rewrite_rules as $pattern => $replacement) {
                if (strpos($pattern, 'check-category') !== false || strpos($replacement, 'check_category') !== false) {
                    echo '<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px;">';
                    echo '<strong>模式:</strong> <code>' . esc_html($pattern) . '</code><br>';
                    echo '<strong>替换:</strong> <code>' . esc_html($replacement) . '</code>';
                    echo '</div>';
                    $found_rules = true;
                }
            }
        }
        
        if (!$found_rules) {
            echo '<p class="status-warning">⚠️ 没有找到相关的重写规则</p>';
            echo '<p>这可能是问题的原因。请尝试刷新重写规则。</p>';
        }
        ?>
    </div>
    
    <div class="card">
        <h2>🛠️ 快速修复</h2>
        
        <p>如果链接不工作，请尝试以下操作：</p>
        
        <a href="<?php echo home_url('/?fix_category_links=1'); ?>" class="test-link">
            修复分类链接
        </a>
        
        <a href="<?php echo admin_url('options-permalink.php'); ?>" class="test-link" target="_blank">
            WordPress固定链接设置
        </a>
        
        <a href="<?php echo home_url('/admin-check-nav/'); ?>" class="test-link" target="_blank">
            返回行政检查导航
        </a>
    </div>
    
    <div class="card">
        <h2>📝 测试说明</h2>
        
        <h3>如何测试:</h3>
        <ol>
            <li>点击上面的"测试链接"按钮</li>
            <li>检查链接是否能正常打开</li>
            <li>确认分类页面显示正确的内容</li>
            <li>检查面包屑导航是否正常</li>
        </ol>
        
        <h3>预期结果:</h3>
        <ul>
            <li>✅ 所有分类链接都应该是绿色（成功）状态</li>
            <li>✅ 点击链接应该打开对应的分类页面</li>
            <li>✅ 分类页面应该显示该分类下的检查项目</li>
            <li>✅ 如果没有项目，应该显示"暂无检查项目"</li>
        </ul>
        
        <h3>常见问题:</h3>
        <ul>
            <li><strong>404错误:</strong> 重写规则问题，需要刷新固定链接</li>
            <li><strong>链接错误:</strong> 分类法配置问题</li>
            <li><strong>页面空白:</strong> 模板文件问题</li>
        </ul>
    </div>
</div>

<?php wp_footer(); ?>
</body>
</html>
