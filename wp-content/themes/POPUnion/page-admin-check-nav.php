<?php
/*
Template Name: 行政检查导航页
*/
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<div class="admin-check-page">
    <!-- 山景背景头部 -->
    <div class="hero-section">
        <div class="mountain-bg"></div>
        <div class="hero-overlay">
            <!-- 主标题 -->
            <div class="main-title">
                <h1>乌鲁木齐县涉企行政检查专栏</h1>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="container">
            <!-- 顶部：检查类别（6类） -->
            <div class="category-nav">
                <?php
                $check_categories = get_terms(array(
                    'taxonomy' => 'check_category',
                    'hide_empty' => false,
                    'meta_key' => 'sort',
                    'orderby' => 'meta_value_num',
                    'order' => 'ASC'
                ));
                $category_icons = array(
                    '行政检查主体' => 'icon-check-body',
                    '行政检查事项和依据' => 'icon-check-items',
                    '行政检查频次上限' => 'icon-check-frequency',
                    '行政检查标准' => 'icon-check-standard',
                    '检查计划' => 'icon-check-plan',
                    '检查文书' => 'icon-check-docs'
                );
                foreach ($check_categories as $index => $category) :
                    $is_active = $index === 0 ? 'active' : '';
                    $icon_class = isset($category_icons[$category->name]) ? $category_icons[$category->name] : 'icon-default';
                    $cate_id = get_term_meta($category->term_id, 'cate_id', true);
                    $category_link = get_term_link($category);
                ?>
                    <a href="<?php echo esc_url($category_link); ?>" class="category-card-link">
                        <div class="category-card <?php echo $is_active; ?>" data-cate-id="<?php echo esc_attr($cate_id); ?>" data-category-name="<?php echo esc_attr($category->name); ?>">
                            <div class="category-icon <?php echo $icon_class; ?>"></div>
                            <h3><?php echo esc_html($category->name); ?></h3>
                            <div class="category-underline"></div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- 顶部：行政检查主体（乡镇/街道） -->
            <div class="subjects-section">
                <h3 class="subjects-title">行政检查主体</h3>
                <div class="subjects-grid" id="subjects-container">
                    <div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">正在加载主体...</div>
                </div>
            </div>

            <!-- 下方：左侧政府部门滚动列表 + 右侧最新15条检查项目 -->
            <div class="portal-grid">
                <div class="portal-col portal-col-left">
                    <h3 class="portal-title">政府部门</h3>
                    <div class="scroll-list" id="gov-dept-list">
                        <div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">正在加载部门...</div>
                    </div>
                </div>
                <div class="portal-col portal-col-right">
                    <h3 class="portal-title" id="portal-right-title">最新检查项目</h3>
                    <div class="portal-items" id="portal-items">
                        <div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">请选择上方主体或左侧部门</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-section">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-info">
                    <h3>主办：乌鲁木齐县人民政府</h3>
                    <p>地址：乌鲁木齐县南华路99号</p>
                    <p>邮编：830000</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>新ICP备16000179号-1&nbsp;&nbsp;网站标识码：6501210001&nbsp;&nbsp;新公网安备65012102000007号</p>
            </div>
        </div>
    </div>
</div>

<style>
/* 基础样式重置 */
.admin-check-page {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    width: 100%;
    background-color: #ffffff;
}

/* 英雄区域 - 山景背景 */
.hero-section {
    position: relative;
    height: 450px;
    overflow: hidden;
}

.mountain-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('<?php echo get_template_directory_uri(); ?>/images/bg-1.jpg');
    background-size: cover;
    background-position: top center;
    background-repeat: no-repeat;
}

/* 移除原来的人工山景效果，使用真实背景图 */

.hero-overlay {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.3);
}

/* 主标题 */
.main-title {
    text-align: center;
}

.main-title h1 {
    font-size: 48px;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 3px 6px rgba(0,0,0,0.6);
    letter-spacing: 2px;
    line-height: 1.2;
}

/* 第一级导航 - 6个图标卡片 */
.category-nav {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.category-card-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.category-card {
    background: white;
    border-radius: 8px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 130px;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

.category-card:hover {
    background: rgba(255,255,255,1);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.category-card.active {
    background: rgba(255,255,255,1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-card.active .category-underline {
    width: 100%;
    background: #4a90e2;
}

.category-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 15px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-icon,
.category-card.active .category-icon {
    opacity: 1;
}

/* 图标样式 */
.icon-check-body { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-1.png'); }
.icon-check-items { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-2.png'); }
.icon-check-frequency { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-3.png'); }
.icon-check-standard { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-4.png'); }
.icon-check-plan { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-5.png'); }
.icon-check-docs { background-image: url('<?php echo get_template_directory_uri(); ?>/images/jiancha-6.png'); }

.category-card h3 {
    font-size: 14px;
    color: #333;
    margin: 0;
    font-weight: 600;
    line-height: 1.4;
}

.category-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 0;
    background: transparent;
    transition: all 0.3s ease;
}

/* 主内容区域 */
.main-content {
    background: white;
    min-height: calc(100vh - 450px);
    padding: 0;
    margin-top: -100px;
}

.container {
    margin: 0 auto;
    padding: 40px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    max-width: 1200px;
    position: relative;
    z-index: 3;
}

/* 使用主题的 inner 类来保持宽度一致 */
.admin-check-page .container,
.admin-check-page .hero-overlay .category-nav {
    width: 100%;
    margin: 0 auto;
}

/* 响应式宽度设置，与主题保持一致 */
@media only screen and (min-width:1440px) {
    .admin-check-page .container,
    .admin-check-page .hero-overlay .category-nav {
        width: 1280px;
    }
}

@media only screen and (min-width:1025px) and (max-width:1439px) {
    .admin-check-page .container,
    .admin-check-page .hero-overlay .category-nav {
        width: 1000px;
    }
}

@media only screen and (max-width:1024px) {
    .admin-check-page .container,
    .admin-check-page .hero-overlay .category-nav {
        width: 960px;
    }
}

@media only screen and (max-width:960px) {
    .admin-check-page .container,
    .admin-check-page .hero-overlay .category-nav {
        width: 96%;
    }
}



/* 第二级导航 - 区域选项卡 */
.region-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    background: white;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    border: none; /* 移除任何边框 */
}

.region-tab {
    flex: 1;
    padding: 12px 24px;
    text-align: center;
    cursor: pointer;
    border-radius: 6px;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
    font-size: 16px;
}

.region-tab:hover {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
}

.region-tab.active {
    background: #4a90e2;
    color: white;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* 主体与门户布局 */
.subjects-section { background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 10px rgba(0,0,0,.1); margin-bottom:20px; }
.subjects-title { 
    margin: 20px 0 40px; 
    font-size: 20px; 
    font-weight: 700; 
    color: #2c3e50; 
    text-align: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.subjects-title::before{
    content: '';
    flex: 1;
    height: 6px;
    background: #4a90e2;
    margin: 0 20px 0 0;
}

.subjects-title::after {
    content: '';
    flex: 1;
    height: 6px;
    background: #4a90e2;
    margin: 0 0 0 20px;
}
.subjects-grid { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); 
    gap: 12px; 
    align-items: stretch;
}
.subject-chip { 
    display: flex; 
    align-items: center; 
    justify-content: center;
    width: 100%; 
    padding: 12px 16px; 
    background: #fff; 
    border: 1px solid #e9ecef; 
    border-radius: 24px; 
    text-align: center; 
    cursor: pointer; 
    transition: all .2s ease;
    font-size: 14px;
    font-weight: 500;
    min-height: 44px;
    box-sizing: border-box;
}
.subject-chip:hover, .subject-chip.active { background:#4a90e2; color:#fff; border-color:#4a90e2; }

.portal-grid { 
    display: flex; 
    gap: 20px; 
    width: 100%;
    box-sizing: border-box;
}

.portal-col-left {
    flex: 0 0 calc(33.333% - 10px);
}

.portal-col-right {
    flex: 0 0 calc(66.667% - 10px);
}
.portal-col { 
    background: #fff; 
    border-radius: 8px; 
    box-shadow: 0 2px 10px rgba(0,0,0,.1); 
    padding: 20px 0; 
    display: flex; 
    flex-direction: column; 
    height: 500px;
    box-sizing: border-box;
    min-width: 0; /* 防止内容溢出 */
}
.portal-title { 
    margin: 0 20px 12px 20px; 
    font-size: 20px; 
    font-weight: 700; 
    color: #2c3e50; 
    flex-shrink: 0;
    position: relative;
    padding-bottom: 20px;
}

.portal-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: #4a90e2;
}
.scroll-list { 
    flex: 1; 
    overflow-y: auto; 
    overflow-x: hidden;
    padding: 0;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    margin: 0 20px;
}
.scroll-list::-webkit-scrollbar { 
    width:0; 
    height:0; 
    display: none; /* Chrome, Safari, Opera */
}
.scroll-item { 
    padding: 14px 20px; 
    margin: 0; 
    background: #f7f9fc; 
    border: 1px solid #e9ecef; 
    border-radius: 8px; 
    cursor: pointer; 
    transition: all .2s ease;
    width: 100%;
    box-sizing: border-box;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    display: block;
    margin-bottom: 6px;
}
.scroll-item:hover, .scroll-item.active { background:#4a90e2; color:#fff; border-color:#4a90e2; }
.portal-items { 
    display: flex; 
    flex-direction: column; 
    gap: 10px; 
    flex: 1; 
    overflow-y: auto; 
    overflow-x: hidden;
    padding: 0 20px 0 20px;
    margin: 0;
}

/* 最新检查项目滚动条样式 */
.portal-items::-webkit-scrollbar {
    width: 6px;
}

.portal-items::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.portal-items::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.portal-items::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
.portal-item { 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    gap: 12px; 
    padding: 12px 14px; 
    background: #fafbfc; 
    border: 1px solid #eef2f6; 
    border-radius: 6px; 
    margin: 0; 
}
.portal-item a { 
    color: #2c3e50; 
    text-decoration: none; 
    flex: 1; 
    min-width: 0; 
    overflow: hidden; 
    text-overflow: ellipsis; 
    white-space: nowrap; 
}
.portal-item a:hover { 
    color: #4a90e2; 
}
.portal-item .date { 
    color: #6c757d; 
    font-size: 12px; 
    white-space: nowrap; 
    flex-shrink: 0; 
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-title h1 {
        font-size: 36px;
    }
    
    .container {
        padding: 30px;
        margin: 0 20px;
    }
    
    .category-nav {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
    
    .portal-grid { 
        flex-direction: column; 
        gap: 15px;
    }
    
    .portal-col-left,
    .portal-col-right {
        flex: 1 1 auto;
    }
    
    .portal-col { 
        height: 400px; 
    }
    
    .subjects-grid { 
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); 
        gap: 10px; 
    }
    
    .subject-chip { 
        padding: 10px 12px; 
        font-size: 13px;
        min-height: 40px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 350px;
    }
    
    .main-title h1 {
        font-size: 28px;
        letter-spacing: 1px;
    }
    
    .container {
        padding: 25px;
        margin: 0 15px;
    }
    
    .category-nav {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .category-card {
        padding: 15px 10px;
        min-height: 100px;
    }
    
    .category-card h3 {
        font-size: 12px;
    }
    
    .region-tabs {
        flex-direction: column;
        gap: 5px;
    }
    
    .portal-grid { 
        flex-direction: column; 
        gap: 12px;
    }
    
    .portal-col-left,
    .portal-col-right {
        flex: 1 1 auto;
    }
    
    .portal-col { 
        height: 350px; 
    }
    
    .subjects-grid { 
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); 
        gap: 8px; 
    }
    
    .subject-chip { 
        padding: 8px 10px; 
        font-size: 12px;
        min-height: 36px;
        border-radius: 18px;
    }
}

@media (max-width: 480px) {
    .hero-section {
        height: 300px;
    }
    
    .main-title h1 {
        font-size: 22px;
        letter-spacing: 0.5px;
    }
    
    .container {
        padding: 20px;
        margin: 0 10px;
    }
    
    .category-nav {
        grid-template-columns: 1fr;
    }
    
    .subjects-grid { 
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); 
        gap: 6px; 
    }
    
    .subject-chip { 
        padding: 6px 8px; 
        font-size: 11px;
        min-height: 32px;
        border-radius: 16px;
    }
    
    .portal-col { 
        height: 300px; 
    }
    
    .grid-container {
        grid-template-columns: repeat(auto-fit, 160px);
        justify-content: center;
    }
    
    .department-card {
        width: 160px;
        min-height: 50px;
        padding: 12px 8px;
        font-size: 12px;
    }
    
    .footer-section {
        margin: 0 10px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 30px;
    }
    
    .footer-links ul {
        text-align: center;
    }
}

/* 底部版权信息 */
.footer-section {
    background: #2c3e50;
    color: white;
    padding: 60px 0;
    margin-top: 80px;
    min-height: 300px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.footer-content {
    text-align: center;
    margin-bottom: 40px;
}

.footer-info h3 {
    color: #3498db;
    font-size: 24px;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-info p {
    margin: 8px 0;
    font-size: 14px;
    color: #bdc3c7;
    line-height: 1.6;
}


.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    margin: 5px 0;
    font-size: 13px;
    color: #95a5a6;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Ajax设置
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    var nonce = '<?php echo wp_create_nonce('admin_check_nonce'); ?>';
    
    var selectedCategory = '';
    var selectedRegion = '';
    var selectedDepartment = '';
    
    // 类别图标映射
    var categoryIcons = {
        '行政检查主体': 'icon-check-body',
        '行政检查事项和依据': 'icon-check-items', 
        '行政检查频次上限': 'icon-check-frequency',
        '行政检查标准': 'icon-check-standard',
        '检查计划': 'icon-check-plan',
        '检查文书': 'icon-check-docs'
    };
    
    // 初始化：加载主体与部门
    loadSubjects();
    loadDepartments();
    
    // 顶部分类类别点击：直接跳转到分类页面
    $(document).on('click', '.category-card', function(e) {
        // 如果用户按住Ctrl或Cmd键，允许在新标签页打开
        if (e.ctrlKey || e.metaKey) {
            return true;
        }

        // 否则在当前页面跳转
        var $link = $(this).closest('.category-card-link');
        if ($link.length && $link.attr('href')) {
            window.location.href = $link.attr('href');
        }
        return false;
    });
    
    // 点击主体（乡镇/街道）
    $(document).on('click', '.subject-chip', function() {
        $('.subject-chip').removeClass('active');
        $(this).addClass('active');
        selectedDepartment = '';
        selectedCategory = $(this).data('category') || '';
        $('#portal-right-title').text($(this).text() + ' - 最新检查项目');
        loadItems({cate_id: selectedCategory});
    });
    
    // 选择部门
    $(document).on('click', '.scroll-item', function() {
        $('.scroll-item').removeClass('active');
        $(this).addClass('active');
        var departmentId = $(this).data('department');
        $('#portal-right-title').text($(this).text() + ' - 最新检查项目');
        loadItems({dept_id: departmentId});
    });
    
    // 加载主体（乡镇/街道）
    function loadSubjects() {
        // 读取 region_type 中“乡（镇）”所关联的 department 列表
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: { action: 'get_departments_by_region', region: '乌鲁木齐县乡（镇）', nonce: nonce },
            success: function(res){
                var html = '';
                if (res.success && res.data.length) {
                    res.data.forEach(function(item){
                        // 主体不需要 dept_id 筛选，使用 cate_id=1（行政检查主体）
                        html += '<div class="subject-chip" data-category="1">' + item.name + '</div>';
                    });
                } else {
                    html = '<div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">暂无主体</div>';
                }
                $('#subjects-container').html(html);
            }
        });
    }

    // 加载政府部门（左侧滚动）
    function loadDepartments() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: { action: 'get_departments_by_region', region: '乌鲁木齐县政府部门', nonce: nonce },
            success: function(res){
                var html = '';
                if (res.success && res.data.length) {
                    res.data.forEach(function(item){
                        html += '<div class="scroll-item" data-department="' + item.department_id + '">' + item.name + '</div>';
                    });
                } else {
                    html = '<div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">暂无部门</div>';
                }
                $('#gov-dept-list').html(html);
            }
        });
    }

    // 加载检查项目到右侧（最多15条）
    function loadItems(params) {
        params = params || {};
        params.action = 'get_check_items_by_filters';
        params.per_page = 15;
        params.nonce = nonce;
        $('#portal-items').html('<div class="placeholder-text" style="text-align:center;color:#666;padding:20px;">正在加载...</div>');
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: params,
            success: function(resp){
                if (resp.success && resp.data.items && resp.data.items.length) {
                    var html = '';
                    resp.data.items.forEach(function(it){
                        html += '<div class="portal-item">';
                        html += '<a href="' + it.url + '">' + it.title + '</a>';
                        html += '<span class="date">' + it.date + '</span>';
                        html += '</div>';
                    });
                    $('#portal-items').html(html);
                } else {
                    $('#portal-items').html('<div class="placeholder-text" style="text-align:center;color:#999;padding:20px;">暂无数据</div>');
                }
            },
            error: function(){
                $('#portal-items').html('<div class="placeholder-text" style="text-align:center;color:#e74c3c;padding:20px;">加载失败</div>');
            }
        });
    }
});
</script>

<?php wp_footer(); ?>
</body>
</html> 