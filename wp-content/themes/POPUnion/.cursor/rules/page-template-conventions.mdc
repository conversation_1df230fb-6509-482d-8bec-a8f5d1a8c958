---
globs: page-*.php,single-*.php,archive-*.php,taxonomy-*.php
description: WordPress页面模板文件命名和结构约定
---

# WordPress模板文件约定

## 模板命名规则

### 页面模板 (page-*.php)
- `page-{slug}.php` - 特定页面的模板
- `page-{id}.php` - 特定ID页面的模板
- `page-{template}.php` - 自定义页面模板

### 自定义文章类型模板
- `single-{post_type}.php` - 单个文章页面
- `archive-{post_type}.php` - 归档页面

### 分类法模板
- `taxonomy-{taxonomy}.php` - 特定分类法
- `taxonomy-{taxonomy}-{term}.php` - 特定分类项

## 本主题特殊模板

### 政府信息公开
- [page-gongkai.php](mdc:page-gongkai.php) - 信息公开主页
- [gongkai.php](mdc:gongkai.php) - 公开信息自定义文章类型注册

### 行政检查系统
- [page-admin-check-nav.php](mdc:page-admin-check-nav.php) - 检查导航页面
- [page-admin-check-list.php](mdc:page-admin-check-list.php) - 检查列表页面
- [archive-admin_check.php](mdc:archive-admin_check.php) - 检查归档页面
- [taxonomy-check_category.php](mdc:taxonomy-check_category.php) - 检查类别页面

## 模板结构最佳实践

### 标准模板结构
```php
<?php get_header(); ?>

<div class="container">
    <!-- 页面内容 -->
    <?php if (have_posts()) : ?>
        <?php while (have_posts()) : the_post(); ?>
            <!-- 文章内容 -->
        <?php endwhile; ?>
    <?php endif; ?>
</div>

<?php get_footer(); ?>
```

### AJAX功能页面
- 包含必要的JavaScript代码
- 使用 `wp_localize_script()` 传递AJAX URL和nonce
- 确保正确的错误处理

### 样式和脚本加载
- 特殊页面的样式应在 `functions.php` 中条件加载
- 避免在模板文件中直接引入CSS/JS文件

## 编辑注意事项
- 始终保持WordPress模板层次结构
- 使用WordPress标准函数和钩子
- 确保模板在不同设备上的响应式显示
- 遵循主题的CSS类命名约定