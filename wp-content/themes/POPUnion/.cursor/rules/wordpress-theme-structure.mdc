---
alwaysApply: true
description: POPUnion WordPress主题项目结构和组织规则
---

# POPUnion WordPress主题结构指南

## 主要文件和目录
- [style.css](mdc:style.css) - 主题样式表，包含主题信息
- [functions.php](mdc:functions.php) - 主题核心功能，包含自定义文章类型、AJAX处理、样式脚本加载
- [index.php](mdc:index.php) - 默认模板文件
- [header.php](mdc:header.php) / [footer.php](mdc:footer.php) - 通用头部和底部模板

## 资源目录
- `css/` - 样式文件，包含主题CSS和政府公开专用样式
- `js/` - JavaScript文件，包含主题JS和第三方库
- `images/` - 图片资源
- `font/` / `fontawesome/` - 字体文件

## 特殊页面模板
- [page-gongkai.php](mdc:page-gongkai.php) - 政府信息公开页面
- [page-admin-check-nav.php](mdc:page-admin-check-nav.php) - 行政检查导航页面
- [page-admin-check-list.php](mdc:page-admin-check-list.php) - 行政检查列表页面
- [archive-admin_check.php](mdc:archive-admin_check.php) - 行政检查归档页面
- [taxonomy-check_category.php](mdc:taxonomy-check_category.php) - 检查类别分类页面

## 配置目录
- `options/` - 后台选项配置文件，包含主题设置和CMB2元框

## 主题特色
- 基于CMB2构建的后台配置系统
- 政府信息公开模块
- 行政检查管理系统
- 响应式设计
- 内置SEO优化功能