@charset "utf-8";
/*去掉下面一行注释,可使整个网站群页面变灰*/
/*body{filter:gray}*/
body {font-size: 14px;font-family: "微软雅黑"}
ul,ol,form,h1,h2,h3,h4,h5,h6,p{padding:0; margin:0; list-style-type:none;}
/* #warp {-moz-box-shadow:0px 2px 13px #333333; -webkit-box-shadow:0px 2px 13px #333333; box-shadow:0px 2px 13px #333333;} */
/*全局样式*/
li{list-style-type:none;list-style:none;}
table{ margin:0 auto;border-spacing:0; border-collapse:collapse; }
img{border:0;}
ol,ul {list-style:none;}
caption,th {text-align:left;}
q:before,q:after {content:'';}   
abbr,acronym {border:0;}
address,caption,cite,code,dfn,em,th,var {font-weight:normal; font-style:normal;}   
.commonList_dot{width: 100%;}
.commonList_dot a {color:#333;text-decoration:none;}
.commonList_dot a:hover {color:#ba2636;text-decoration:underline;} 
.commonList a {color:#333;text-decoration:none;}
.commonList a:hover {color:#ba2636;text-decoration:underline;} 
.commonList-bottom-line li{border-bottom:1px dotted #ccc;}

.fB{font-weight:bold;}
.f12px{font-size:12px;}
.f13px{font-size:13px;}
.f14px{font-size:14px;}
.f16px{font-size:16px;}
.f13B{ font-size:13px; font-weight:bold; }
.f14B{ font-size:14px; font-weight:bold; }
.f16B{ font-size:16px; font-weight:bold; }
.f_red{ color:#FC3804;}
.f_yellow{ color:#FFEA00;}
.f_fff{ color:#FFF;}

.red{ border:1px solid red;}
.blue{ border:1px solid blue;}
.left{ float:left;}
.right{ float:right;}
.pointer{cursor:pointer;}
.textLeft{ text-align:left;}
.textRight{ text-align:right;}
.textCenter{ text-align:center}
.clearBoth{clear:both;}

/*横向间隔定义*/
.blank0 {clear:both; height:0px; overflow:hidden; display:block;}
.blank1 {clear:both; height:1px; overflow:hidden; display:block;}
.blank2 {clear:both; height:2px; overflow:hidden; display:block;}
.blank3 {clear:both; height:3px; overflow:hidden; display:block;}
.blank4 {clear:both; height:4px; overflow:hidden; display:block;}
.blank5 {clear:both; height:5px; overflow:hidden; display:block;}
.blank6 {clear:both; height:6px; overflow:hidden; display:block;}
.blank7 {clear:both; height:7px; overflow:hidden; display:block;}
.blank8 {clear:both; height:8px; overflow:hidden; display:block;}
.blank9 {clear:both; height:9px; overflow:hidden; display:block;}
.blank10 {clear:both; height:10px; overflow:hidden; display:block;}
.blank12 {clear:both; height:12px; overflow:hidden; display:block;}
.blank15 {clear:both; height:15px; overflow:hidden; display:block;}
.blank18 {clear:both; height:18px; overflow:hidden; display:block;}

/*内容盒子，主要用于定位好后的Div内部*/
.contentBox1{ padding:1px;}
.contentBox2{ padding:2px;}
.contentBox3{ padding:3px;}
.contentBox4{ padding:4px;}
.contentBox5{ padding:5px;}
.contentBox6{ padding:6px;}
.contentBox6{ padding:7px;}
.contentBox6{ padding:8px;}
.contentBox9{ padding:9px;}
.contentBox10{ padding:10px;}
.contentBox12{ padding:12px;}
.contentBox15{ padding:15px;}
.contentBox18{ padding:18px;}

/*文字缩进*/
.textIndent2em{ text-indent:2em;}


.hidden {display:none;}
.display{display:block;}


/*内容列表通用修饰符*/
.commonList li{line-height:24px;}
.commonList_dot li{line-height:24px; width: 100%; background:url(../images/ico_dot.gif) left no-repeat; text-indent:10px;}
.commonList_video li{line-height:24px; background:url(../images/ico_video.gif) left no-repeat; text-indent:20px;}
.commonList_round li{line-height:24px; background:url(../images/ico_round.gif) left no-repeat; text-indent:15px;}
.commonList li span.a_date{ float:right; padding-right:6px;}
.commonList_dot li .a_date{ float:right; padding-right:6px;}
.commonList_video li span{ float:right; padding-right:6px;}
.commonList_round li span{ float:right; padding-right:6px;}
.commonList li.line{clear:both; height:1px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x;}
.commonList_dot li.line{clear:both; height:1px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x;}
.commonList_video li.line{clear:both; height:1px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x;}
.commonList_round li.line{clear:both; height:1px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x;}

/*行高定义*/
.lineHeight140{line-height:140%;}
.lineHeight160{line-height:160%;}/*常用于新闻正文*/
.lineHeight180{line-height:180%;}
.lineHeight200{line-height:200%;}
.lineHeight20px{ line-height:20px;}
.lineHeight23px{ line-height:23px;}
.lineHeight24px{ line-height:24px;}/*常用于新闻列表*/
.lineHeight25px{ line-height:25px;}
.lineHeight28px{ line-height:28px;}


/*虚线隔行*/
.line1px{clear:both; height:1px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x; }
.line2px{clear:both; height:2px; overflow:hidden; display:block; background:url(../images/ico_line.gif) repeat-x; }

/*底部版权*/
#footer{width:100%;height:auto;margin:0px auto;clear:both;padding: 10px 0;}
.footer_zt{width:100%;float:left; text-align:left;font:14px/25px "Microsoft Yahei"; color:#666767; }


/*其他公用*/
.hidden {display: none;}
/*导航*/
/*阴影*/
.boxshow { 
	border: 1px solid #E1E1E1;
           box-shadow: 0 0 2px rgba(0,0,0,0.1);
           background: #fff;
           border-top: none;
}
/*分割线*/
.dashedline {    
	border-bottom: 1px dashed #f0f0f0;
	height: 12px;
	line-height: 12px;
	padding: 4px;
	margin-bottom: 6px;
}



/*白色按钮*/
.am-btn-huise {
  color: #444444;
  background-color: #f9f9f9;
  border:1px solid #e1e1e1;
  box-shadow: 0 0 2px rgba(0,0,0,0.1);
}
a.am-btn-huise:visited {
  color: #444;
}
a.am-btn-huise:hover {
  color: #fff;
}
.am-btn-huise:hover,
.am-btn-huise:focus,
.am-btn-huise:active,
.am-btn-huise.am-active,
.am-active .am-btn-huise.am-dropdown-toggle{
  color: #fff;
  border-color: #c7c7c7;
}
.am-btn-huise:hover,
.am-btn-huise:focus {
  background-color: #3bb4f2;
  color: #fff;
}
.am-btn-huise:active,
.am-btn-huise.am-active,
.am-active .am-btn-huise.am-dropdown-toggle {
  background-image: none;
  background-color: #c2c2c2;
}
.am-btn-huise.am-disabled,
.am-btn-huise[disabled],
fieldset[disabled] .am-btn-huise,
.am-btn-huise.am-disabled:hover,
.am-btn-huise[disabled]:hover,
fieldset[disabled] .am-btn-huise:hover,
.am-btn-huise.am-disabled:focus,
.am-btn-huise[disabled]:focus,
fieldset[disabled] .am-btn-huise:focus,
.am-btn-huise.am-disabled:active,
.am-btn-huise[disabled]:active,
fieldset[disabled] .am-btn-huise:active,
.am-btn-huise.am-disabled.am-active,
.am-btn-huise[disabled].am-active,
fieldset[disabled] .am-btn-huise.am-active {
  background-color: #e6e6e6;
  border-color: #e6e6e6;
}
.am-btn-group .am-btn-huise,
.am-btn-group-stacked .am-btn-huise {
  border-color: #d9d9d9;
}

/*面板标题背景色*/
.am-panel .am-panel-hd-primary {background: #0e90d2;}
.am-panel .am-panel-hd-secondary {background: #3bb4f2;}
.am-panel .am-panel-hd-success {background: #4aaa4a;}
.am-panel .am-panel-hd-warning {background: #e0690c;}
.am-panel .am-panel-hd-danger {background: #dd514c;}

/*文字颜色*/
.am-text-white {color:#fff;}
.am-text-white p,.am-text-white span{color:#fff;}

/*白色链接*/
a.wihte-link:link,a.wihte-link:visited {color:#fff;}
a.wihte-link:hover{color:#fff;}

.bg-default {
  color: #444;
  background-color: #f1f1f1;
}
a.bg-default:hover {
  background-color: #f1f1f1;
}

.bg-primary {
  color: #fff;
  background-color: #428bca;
}
a.bg-primary:hover {
  background-color: #3071a9;
}
.bg-secondary {
  color: #fff;
  background-color: #19a7f0;
}
a.bg-secondary:hover {
  background-color: #19a7f0;
}
.bg-success {
  background-color: #dff0d8;
}
a.bg-success:hover {
  background-color: #c1e2b3;
}
.bg-info {
  background-color: #d9edf7;
}
a.bg-info:hover {
  background-color: #afd9ee;
}
.bg-warning {
  background-color: #fcf8e3;
}
a.bg-warning:hover {
  background-color: #f7ecb5;
}
.bg-danger {
  background-color: #f2dede;
}
a.bg-danger:hover {
  background-color: #e4b9b9;
}
@media only screen and (max-width: 640px) {
body {
background: #fff;
  }

  