@charset "utf-8";
body.page-template-page-gongkai, body.single-opens, body.tax-openscat {
    background-image: url("../images/wlmq/banner.png");
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100%;
}

.am-container {
    background: none;
}
/*滚动条*/
.scrollformate::-webkit-scrollbar {
    box-sizing: border-box;
    width: 10px;
    background: #fff;
}

.scrollformate::-webkit-scrollbar-button {
    display: none
}

.scrollformate::-webkit-scrollbar-track-piece {
    background-color: #fff;
}

.scrollformate::-webkit-scrollbar-thumb {
    background-color: #2d66a5;
    border-radius: 5px;
    position: relative;
}

.scrollformate::-webkit-scrollbar-corner {
    background-color: #fff;
}

/*header*/

.header {
    height: 365px;
}

.header .logo {
    padding: 15px 0px 15px 0px;
    line-height: 74px;
    background-image: url("../../images/standard_gk/logo2.png");
    background-repeat: no-repeat;
    background-position: left;
    padding-left: 79px;
    font-size: 35px;
    color: #fff;
}

.header .page-header {
    color: #fff;
    text-align: center;
    font-size: 100px;
    position: relative;
    font-weight: 600;
    text-shadow: 0px 5px 0px rgba(0, 0, 0, 0.2);
}

.header .page-header:after {
     height:2px;
     width: 488px;
    content: ' ';
    position: absolute;
    bottom: 0px;
    left: 50%;
    background: white;
}

/*mainbox*/
.main {
    background: #fff;
    padding: 0px 36px;
    margin-bottom: 15px;
}

.searchbox {
    width: 100%;
    padding: 86px 0px;
    position: relative;
}

.searchbox:before {
    content: ' ';
    width: 240px;
    height: 2px;
    background: rgba(224, 224, 224, 1);
    position: absolute;
    top: 50%;
    margin-top: -1px;
    left: 0px;
}

.searchbox:after {
    content: ' ';
    width: 240px;
    height: 2px;
    background: rgba(224, 224, 224, 1);
    position: absolute;
    top: 50%;
    margin-top: -1px;
    right: 0px;
}

.inputbox {
    margin: 0 auto;
    width: 510px;
    border-radius: 9px;
    border: 2px solid rgba(45, 102, 165, 0.92);
    background: #fff;
    font-size: 0px;
    overflow: hidden;

}

.inputbox input {
    border: none;
    height: 46px;
    width: 460px;
    line-height: 46px;
    text-indent: 10px;
    outline: none;
    font-size: 16px;
}

.inputbox input::-webkit-input-placeholder {
    font-weight: 600;
    color: #2d66a5
}


.inputbox button {
    outline: none;
    cursor: pointer;
    height: 46px;
    width: 40px;
    border: none;
    background-image: url("../images/wlmq/searchbtn.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 80%;
}

/*左侧*/
.zfxxgk_left {
    width: 240px;
    float: left;
}

.zfxxgk_left > ul > li {
    margin-bottom: 15px;
    border: 1px solid #fff;
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.35);
}

.zfxxgk_left > ul > li > a {
    width: 100%;
    height: 68px;
    background: #fff;
    color: rgb(45, 102, 165);
    position: relative;
    display: table-cell;
    vertical-align: middle;
    cursor: pointer;
    font-size: 23px;
    line-height: 30px;
    padding: 3px 43px 0px 76px;
}

.zfxxgk_left > ul > li:hover > a, .zfxxgk_left > ul > li.active > a, 
.zfxxgk_left > ul > li.current-menu-item > a 
{
    background-color: rgb(45, 102, 165);
    color: #fff;
    cursor: pointer;
}

.zfxxgk_left > ul > li > a > i {
    position: absolute;
    left: 15px;
    top: 13px;
    width: 40px;
    height: 50px;
    display: block;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 100%;
}

.zfxxgk_left > ul > li > a > i.zc {
    background-image: url("../images/wlmq/zc.png");
}
.zfxxgk_left > ul > li > a > i.gkzn, .zfxxgk_left > ul > li.zhinan > a > i { /*zhinan*/
    background-image: url("../images/wlmq/gkzn.png");
}

.zfxxgk_left > ul > li > a > i.gkzd,  /*shuji*/
.zfxxgk_left > ul > li.shuji > a > i
{
    background-image: url("../images/wlmq/gkzd.png");
}

.zfxxgk_left > ul > li > a > i.zdgk,/*biji*/
.zfxxgk_left > ul > li.biji > a > i
{
    background-image: url("../images/wlmq/zdgk.png");
}
.zfxxgk_left > ul > li > a > i.ndbb{
   background-image: url("../images/wlmq/ndbb.png");
}
.zfxxgk_left > ul > li > a > i.ysqgk,
.zfxxgk_left > ul > li.dingwei > a > i
{
   background-image: url("../images/wlmq/ysqgk.png");
}
.zfxxgk_left > ul > li > a > i.gjgbm,
.zfxxgk_left > ul > li.chain > a > i
{
   background-image: url("../images/wlmq/gjgbm.png");
}
.zfxxgk_left > ul > li > a > i.zcc, .zfxxgk_left > ul > li.xingbiao > a > i{ /*星标图像*/
   background-image: url("../images/wlmq/zcc.png");
}
.zfxxgk_left > ul > li:hover > a > i.gjgbm, .zfxxgk_left > ul > li.active > a > i.gjgbm{
   background-image: url("../images/wlmq/gjgbm_active.png");
}
.zfxxgk_left > ul > li:hover > a > i.ysqgk, .zfxxgk_left > ul > li.active > a > i.ysqgk{
   background-image: url("../images/wlmq/ysqgk_active.png");
}
.zfxxgk_left > ul > li:hover > a > i.ndbb, .zfxxgk_left > ul > li.active > a > i.ndbb{
   background-image: url("../images/wlmq/ndbb-active.png");
}
.zfxxgk_left > ul > li > a > i.gknb,
.zfxxgk_left > ul > li.wenjianjia > a > i
{
    background-image: url("../images/wlmq/gknb.png");
}
.zfxxgk_left > ul > li > a > i.gkysq{
    background-image: url("../images/wlmq/gkysq.png");
}
.zfxxgk_left > ul > li > a > i.gkbm{
    background-image: url("../images/wlmq/gkbm.png");
}

/*hover*/
.zfxxgk_left > ul > li:hover > a > i.zc, .zfxxgk_left > ul > li.active > a > i.zc {
    background-image: url("../images/wlmq/zc-active.png");
}
.zfxxgk_left > ul > li:hover > a > i.gkzn, .zfxxgk_left > ul > li.active > a > i.gkzn,/*zhinan打开*/
.zfxxgk_left > ul > li.zhinan:hover > a > i, 
.zfxxgk_left > ul > li.zhinan.cur > a > i, 
.zfxxgk_left > ul > li.zhinan.current-menu-item > a > i
{ 
    background-image: url("../images/wlmq/gkzn-active.png");
}

.zfxxgk_left > ul > li:hover > a > i.zcc, .zfxxgk_left > ul > li.active > a > i.zcc, 
.zfxxgk_left > ul > li.dingwei.current-menu-item > a > i,
.zfxxgk_left > ul > li.dingwei.cur > a > i, 
.zfxxgk_left > ul > li.dingwei:hover > a > i{ 
   background-image: url("../images/wlmq/ysqgk_active.png");
}

.zfxxgk_left > ul > li:hover > a > i.zcc, .zfxxgk_left > ul > li.active > a > i.zcc, 
.zfxxgk_left > ul > li.chain.current-menu-item > a > i,
.zfxxgk_left > ul > li.chain.cur > a > i, /*星标打开*/
.zfxxgk_left > ul > li.chain:hover > a > i{ /*星标图像划过*/

   background-image: url("../images/wlmq/gjgbm_active.png");
}

.zfxxgk_left > ul > li:hover > a > i.zcc, .zfxxgk_left > ul > li.active > a > i.zcc, 
.zfxxgk_left > ul > li.xingbiao.current-menu-item > a > i,
.zfxxgk_left > ul > li.xingbiao.cur > a > i, /*星标打开*/
.zfxxgk_left > ul > li.xingbiao:hover > a > i{ /*星标图像划过*/

   background-image: url("../images/wlmq/zcc_active.png");
}
.zfxxgk_left > ul > li:hover > a > i.gkzd, .zfxxgk_left > ul > li.active > a > i.gkzd, /*shuji打开*/
.zfxxgk_left > ul > li.shuji:hover > a > i,
.zfxxgk_left > ul > li.shuji.current-menu-item > a > i,
.zfxxgk_left > ul > li.shuji.cur > a> i
{
    background-image: url("../images/wlmq/gkzd-active_.png");
}

.zfxxgk_left > ul > li:hover > a > i.zdgk, .zfxxgk_left > ul > li.active > a > i.zdgk,
.zfxxgk_left > ul > li.biji:hover > a > i,/*biji*/
.zfxxgk_left > ul > li.biji.cur > a > i,
.zfxxgk_left > ul > li.biji.current-menu-item > a > i
{
    background-image: url("../images/wlmq/zdgk-active.png");
}

.zfxxgk_left > ul > li:hover > a > i.gknb, .zfxxgk_left > ul > li.active > a > i.gknb,
.zfxxgk_left > ul > li.wenjianjia:hover > a > i,
.zfxxgk_left > ul > li.wenjianjia.cur > a > i,
.zfxxgk_left > ul > li.wenjianjia.current-menu-item > a > i
{
    background-image: url("../images/wlmq/gknb-active.png");
}
.zfxxgk_left > ul > li:hover > a > i.gkysq, .zfxxgk_left > ul > li.active > a > i.gkysq {
    background-image: url("../images/wlmq/gkysq-active.png");
}
.zfxxgk_left > ul > li:hover > a > i.gkbm, .zfxxgk_left > ul > li.active > a > i.gkbm{
    background-image: url("../images/wlmq/gkbm-active.png");
}

.zfxxgk_left .xxgk_live1 .am-collapsed i.more1 {
    position: absolute;
    left: unset;
    right: 15px;
    top: 10px;
    width: 20px;
    height: 68px;
font: initial;
    font-size: 30px;
    display: block;
  /*  background-image: url("../../images/standard_gk/plus-suo.png");*/
    background-position: center;
    background-repeat: no-repeat;
}
.zfxxgk_left .xxgk_live1 i.more1 {
    position: absolute;
    left: unset;
    right: 15px;
    top: 10px;
    width: 20px;
font: initial;
    font-size: 30px;
    height: 68px;
    display: block;
  /*  background-image: url("../../images/standard_gk/plus.png");*/
    background-position: center;
    background-repeat: no-repeat;
}
.zfxxgk_left .xxgk_live1 i.more2 {
    position: absolute;
    left: unset;
    right: 15px;
    top: 10px;
    width: 20px;
font: initial;
    font-size: 30px;
    height: 68px;
    display: block;
  /*  background-image: url("../../images/standard_gk/plus.png");*/
    background-position: center;
    background-repeat: no-repeat;
}
.zfxxgk_left > ul > li > a > i.more {
    position: absolute;
    left: unset;
    right: 15px;
    top: 0px;
    width: 20px;
    height: 68px;
    display: block;
    background-image: url("../../images/standard_gk/plus.png");
    background-position: center;
    background-repeat: no-repeat;
}

.zfxxgk_left > ul > li:hover > a > i.more ,.zfxxgk_left > ul > li.active:hover > a > i.more {
    background-image: url("../../images/standard_gk/plus-active.png");
}

.zfxxgk_left > ul > li.active > a > i.more{
    background-image: url("../../images/standard_gk/plus-hover.png");
} 
.zfxxgk_left > ul > li > ul{
    transition: all .3s ease-in;
    display: none;
    margin-top: 1px;
    /*padding-left: 40px;*/
    padding: 10px 10px;
    line-height: 32px;
    background: #e0ebef;
    margin: 0;
    list-style: none;

}
.zfxxgk_left > ul > li.active > ul{
    transition: all .3s ease-in;
    /*display: block;*/
}
.zfxxgk_left > ul > li > ul > li {
    font-size: 15px;
    height: 32px;
    line-height: 32px;
    position: relative;
    padding-left: 20px;
    margin-top: 10px;
    background: white;
    font-weight: bold;
}
.zfxxgk_left > ul > li > ul > li:after {
    content: '';
   
}

.zfxxgk_right {
	  float: right;
	  width:770px;
	  box-shadow:0px 1px 5px 0px rgba(0, 0, 0, 0.2);
}

.xxgkzn_title{
	 font-size:32px;
    font-weight:bold;
    color:rgba(45,102,165,1);
    text-align: center;
    margin:38px;
}
.xxgkzn_cont p{
	text-indent:2em;
	line-height: 30px;
}
.xxgkzn_cont p .title_1{
	font-size: 21px;
	font-weight: bold;
	line-height:60px;
}
.xxgkzn_cont p .title_2{
	font-size: 16px;
	font-weight: bold;
	line-height:38px;
}
.zfxxgk_right h4{
	text-align: center;
    font-weight: bold;
}
.zfxxgk_right .infobox {
    max-height: 940px;
    overflow-y: scroll;
    padding: 0px 15px;
}


.zfxxgk_right .listbox {
    max-height: 670px;
    overflow-y: scroll;
    padding: 0px 15px;
}

.zfxxgk_right .listbox .line {
    border-bottom: 2px solid #ddd;
    margin: 22px 0;
}

.zfxxgk_right .listbox > ul > li {
    font-size: 16px;
    line-height: 30px;
}

.zfxxgk_right .listbox > ul > li .date {
    color: #999;
    margin-left: 10px;
}

.zfxxgk_right .sectionbox {
    max-height: 1172px;
    overflow-y: scroll;
    /* padding: 0px 15px; */
   padding-bottom: 20px;
}

.zfxxgk_right .sectionbox .section ul {
    padding: 20px;
}

.zfxxgk_right .sectionbox .section .title {
    border-bottom: 1px solid #ccc;
    /* margin-bottom: 15px; */
}

.zfxxgk_right .sectionbox .section .title span {
    display: inline-block;
    width: 185px;
    height: 35px;
    background: rgba(45, 102, 165, 1);
    font-size: 16px;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    line-height: 36px;
    text-align: center;
}
.zfxxgk_right .sectionbox .section .title .ndhbhz-title{width: 48%;}
.zfxxgk_right .sectionbox .section ul > li {
    list-style: none;
    font-size: 16px;
    line-height: 60px;
}

.zfxxgk_right .sectionbox .section ul > li .date {
    color: #999;
    float: right;
}

.zfxxgk_right .sectionbox .section a.more {
    display: block;
    text-align: center;
    width: 100px;
    height: 30px;
    background: rgba(224, 224, 224, 1);
    border-radius: 5px;
    line-height: 30px;
    font-size: 16px;
    margin: 0 auto;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
}

.zfxxgk_right .xxgkgzbg-list{
margin: 20px;
border: #e7e7e7 solid 0.5px;}

.zfxxgk_right .xxgkgzbg-list li{
padding-right: 10px;
padding-left: 10px;
text-align: center;
border-bottom: #e7e7e7 solid 0.5px;
border-left: #e7e7e7 solid 0.5px;

}
.zct{
	
    line-height: 65px;!important
}
.ysq{
    line-height: 59px;!important
}
.xxgk_live1 span,.xxgk_live1 a span, .zfxxgk_left >ul>li>a>span{
	display: inline-block;
    width: 120px;
}
.xxgk_live2{
    background: #e0ebef;
    padding: 6px 8px;
    margin-top: -15px;
    margin-bottom: 15px;
    line-height:  32px;
}

.xxgk_live2 li{
    font-weight: bold;
    margin-top: 6px;
    background: #fff;
    line-height: 36px;
    padding-left: 15px;
}
.xxgk_live3{
    padding: 10px 10px;
    margin-top: -23px;
    line-height:  32px;
    background: #e0ebef;
}
.xxgk_live3 li{
    margin-top: 10px;
    background: white;
    line-height: 36px;
    padding-left: 21px;
    font-size: 14px;
   font-weight: bold;
}
.xxgk_live2 li.on,.xxgk_live2 li:hover{
    background:rgba(230,245,255,1);
}
.xxgk_live2>li:before {
	content: ".";
	/*position: absolute;
	left: 20rem;
	margin-top: -2px;*/
}

.xxgk_live2 li.on a{
    color: #2D66A5;
}

/*分页*/
#page-div{margin:15px auto;text-align: center;}
.pagination-first,.pagination-last{display: block;margin: 10px;}
.pagination-index,.pagination-num{border: 1px solid #bfbfbf;margin: 0 3px;padding:0 10px;display: inline-block;border-radius: 3px;color: #bfbfbf;}
.pagination-num.active,.pagination-index:hover,.pagination-num:hover{background: #bfbfbf;color: white;}
.pagination-last>span{margin: 0 10px;}
.pagination-last input{height: 18px;width: 30px;text-align: center;}

#xxgk_list3 ul li {
	 border-bottom: none;
         padding-bottom: 16px;
}
/*法定主动公开内容start */
.tabs_t{
    width: 100%;
    overflow: hidden;
    zoom: 1;
    padding: 10px 0 30px 0;
    border-bottom: 1px solid #ccc;
}
.tabs_t li{
    float: left;
    width: 180px;
    height: 64px;
    background: #fff;
    border: 1px solid  #f0f0f0;
    margin: 5px;
    box-shadow:#d8d5d5 2px 2px 5px 1px;
}
.tabs_t li i{
    float: left;
    display: block;
    width: 30px;
    height: 30px;
    margin: 20px 0 0 30px;
    background: url(../../images/standard_gk/icons-xxgk.png) 0 0  no-repeat;
}
.tabs_t .t_lic2 i{
    background-position: 0 -40px;
}
.tabs_t .t_lic3 i{
    background-position: 0 -80px;
}
.tabs_t .t_lic4 i{
    background-position: 0 -120px;
}
.tabs_t .t_lic5{
   /* clear: both;*/
}
.tabs_t .t_lic5 i{
    background-position: 0 -160px;
}
.tabs_t .t_lic6 i{
    background-position: 0 -200px;
}
.tabs_t .t_lic7 i{
    background-position: 0 -240px;
}
.tabs_t .t_lic8 i{
    background-position: 0 -280px;
}
.tabs_t :hover{
    background: #2d66a5;
}
.tabs_t :hover i{
    background: url(../../images/standard_gk/icons-xxgk.png) -78px 0  no-repeat;
}
.tabs_t .t_lic2:hover i{
    background-position: -72px -40px;
}
.tabs_t .t_lic3:hover  i{
    background-position: -72px -80px;
}
.tabs_t .t_lic4:hover  i{
    background-position: -72px -120px;
}
.tabs_t .t_lic5:hover  i{
    background-position: -72px -160px;
}
.tabs_t .t_lic6:hover  i{
    background-position: -72px -200px;
}
.tabs_t .t_lic7:hover  i{
    background-position: -72px -240px;
}
.tabs_t .t_lic8:hover i{
    background-position: -72px -280px;
}
.tabs_t li span{
    float: left;
    display: block;
    margin-left: 10px;
    height: 64px;
    color: #2d66a5;
    font: normal 18px/64px "微软雅黑";
}
.tabs_t :hover span{
  color: #fff;
}
.tabs_t_box{
    padding: 20px 18px 0;
}

.tabs_t2{
width: 100%;
overflow: hidden;
zoom: 1;
padding: 10px 0 30px 0;
border-bottom: 1px solid #ccc;
}
.tabs_t2 li{
float: left;
width: 180px;
height: 64px;
background: #fff;
border: 1px solid  #f0f0f0;
margin: 5px;
box-shadow:#d8d5d5 2px 2px 5px 1px;
}
.tabs_t2 li i{
float: left;
display: block;
width: 30px;
height: 30px;
margin: 20px 0 0 20px;
background: url(../../images/standard_gk/icons-xxgknr.png) 0 0  no-repeat;
}
.tabs_t2 .t2_lic2 i{
background-position: 0 -40px;
}
.tabs_t2 .t2_lic3 i{
background-position: 0 -80px;
}
.tabs_t2 .t2_lic4 i{
background-position: 0 -120px;
}
.tabs_t2 .t2_lic5{
clear: both;
}
.tabs_t2 .t2_lic5 i{
background-position: 0 -160px;
}
.tabs_t2 .t2_lic6 i{
background-position: 0 -200px;
}
.tabs_t2 .t2_lic7 i{
background-position: 0 -240px;
}
.tabs_t2 .t2_lic8 i{
background-position: 0 -280px;
}
.tabs_t2 .t2_lic9 i{
background-position: 0 -320px;
}
.tabs_t2 .t2_lic10 i{
background-position: 0 -360px;
}
.tabs_t2 :hover{
background: #2d66a5;
}
.tabs_t2 :hover i{
background: url(../../images/standard_gk/icons-xxgknr.png) -78px 0  no-repeat;
}
.tabs_t2 .t2_lic2:hover i{
background-position: -72px -40px;
}
.tabs_t2 .t2_lic3:hover  i{
background-position: -72px -80px;
}
.tabs_t2 .t2_lic4:hover  i{
background-position: -72px -120px;
}
.tabs_t2 .t2_lic5:hover  i{
background-position: -72px -160px;
}
.tabs_t2 .t2_lic6:hover  i{
background-position: -72px -200px;
}
.tabs_t2 .t2_lic7:hover  i{
background-position: -72px -240px;
}
.tabs_t2 .t2_lic8:hover i{
background-position: -72px -280px;
}
.tabs_t2 .t2_lic9:hover  i{
background-position: -72px -320px;
}
.tabs_t2 .t2_lic10:hover i{
background-position: -72px -360px;
}
.tabs_t2 li span{
float: left;
display: block;
margin-left: 10px;
height: 64px;
color: #2d66a5;
font: normal 18px/64px "微软雅黑";
}
.tabs_t2 :hover span{
color: #fff;
}
/*法定主动公开内容end */

.zfxxgk_right .am-avg-md-3 > li:nth-child(6n+1),.zfxxgk_right .am-avg-md-3 > li:nth-child(6n+2),.zfxxgk_right .am-avg-md-3 > li:nth-child(6n+3) {
    background-color: rgba(242,242,242,1);
}
.zfxxgk_right .section .xxgkgzbg-title { color: red;font-size: 18px;margin-top: 20px;text-align: left;text-indent: 13px;}
.zfxxgk_right .section .xxgkgzbg-title a{ color: red;}
.zfxxgk_right .section .xxgkgzbg-title a:hover{ color: #2d66a5;}
.zfxxgk_right .section .zfxxgkbg{
height: 47px;
line-height: 47px;
padding-left: 22px;
color: #226ebc;
font-size: 16px;
position: relative;
margin: 10px 0;
}
.zfxxgk_right .section .zfxxgkbg::before{
content: "";
width: 6px;
height: 25px;
position: absolute;
background:#069be5;
border-radius:8px;
left: 10px ;
top: 12px;
}
/* 法定主动公开内容 end*/
@media (max-width: 768px) {
    .searchbox {
        display: none;
    }
.conbox1100 {
   width:100%;
}
    .header {
        height: 128px;
    }

    .header .logo {
        padding-top: 44px;
        line-height: 20px;
        font-size: 16px;
    }

    .header .page-header {
        font-size: 30px;
    }

    .header .page-header:after {
        display: none;
    }

    .main {
        padding: 0px;
    }

    .zfxxgk_left {
        width: 100%;
    }

    .zfxxgk_left > ul {
        display: flex;
        flex-wrap: wrap;
    }

    .zfxxgk_left > ul > li {
        width: 100%;
        margin: 0px;
    }

    .zfxxgk_left > ul > li > a > i.more {
        right: 5px;
    }

    .zfxxgk_left > ul > li.active > ul {
        width: 100%;
    }
.xxgk_live2{
	    width: 100%;
	    box-shadow: none;
	    padding: 0;
	    margin-top: 0;
	    margin-bottom: 0;
	    line-height: 30px;
}
.xxgk_live3{
	    width: 100%;
	    box-shadow: none;
	    padding: 0;
	    margin-top: 0;
	    margin-bottom: 0;
	    line-height: 30px;
}
.xxgk_live3 li{
	    background: white;
            font-size: 14px;
            font-weight: bold;
            margin-top: 0px;
            

}
.xxgk_live2 li{
margin-top: 0;}
  
    .xxgk_live2>li:before {
    	display: none;
    }
    .zfxxgk_left > ul > li > a {
    width: 100%;
    height: 58px;
    background: #fff;
    color: rgb(45, 102, 165);
    position: relative;
    display: table-cell;
    vertical-align: middle;
    cursor: pointer;
    font-size: 18px;
    line-height: 32px;
    padding: 17px 43px 0px 76px;
}

.zfxxgk_right {
  width:100%;
}
.zfxxgk_right .sectionbox .section .title .ndhbhz-title{width: 100%;}
.zfxxgk_right .sectionbox .section ul {
    padding: 0px;
}
.zfxxgk_right .xxgkgzbg-list {
    margin: 0px;
}
.zfxxgk_right .xxgkgzbg-list li {
    padding-right: 5px;
    padding-left: 5px;
}
.xxgk_live1 span, .xxgk_live1 a span {
    width: 100%;
}
}
