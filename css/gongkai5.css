@charset "utf-8";

/*reset*/

ul, p {
    margin: 0px;
    padding: 0px;
    list-style: none;
    outline: none;
}

a {
    color: #333
}

/*站点样式*/
.am-container {
    max-width: 1100px;
}

/*tabs*/

.am-tabs {
    overflow: hidden;
}

.am-tabs .am-tabs-nav {
    position: relative;
    border-bottom-color: #f5f5f5;
}

.am-tabs .am-tabs-nav > li {
    text-align: center;
}

.am-tabs .am-tabs-nav > li > a {
    font-size: 16px;
    color: #333;
    margin-right: 0px;
    padding: .5rem 1rem;
    font-weight: 600;
    border: none;
}

.am-nav-tabs > li > a:hover, .am-nav-tabs > li.am-active > a, .am-nav-tabs > li.am-active > a:focus, .am-nav-tabs > li.am-active > a:hover {
    text-decoration: none;
    background: transparent;
    color: #d93c37;
    cursor: pointer;
    border: none;
    border-bottom: #CA4C47 solid 2px;
}

.am-tabs .am-tabs-nav .more {
    float: right;
    margin-right: 5px;
    color: #999;
    font-size: 14px;
}

.am-tabs .am-tabs-nav .more a {
    color: #999;
    text-decoration: none;
}


.am-tabs .am-tabs-bd {
    border: none;
    overflow: hidden;
}

.am-tabs-bd .am-tab-panel {
    padding: 5px 7px;
}

/*focuse*/
.am-slider-inner .am-slides li {
    position: relative;
}

.am-slider-inner .am-slider-title {
    position: absolute;
    background: #015193;
    color: #fff;
    margin: 0px;
    padding: 5px 10px;
    bottom: 0px;
    left: 0px;
    z-index: 2;
    width: 100%;
    font-weight: 400;
}

.am-slider-inner .am-control-nav {
    width: auto;
    text-align: right;
    right: 10px;
    bottom: 10px;
}

.am-slider-inner .am-control-nav li a {
    background-color: rgba(255, 255, 255, .7);
    width: 12px;
    height: 12px;
}

.am-slider-inner .am-control-nav li a:hover {
    background-color: rgb(255, 255, 255);
}

.am-slider-inner .am-control-nav li a.am-active {
    background-color: #d93c37;
}

.am-slider-inner .am-control-nav,
.am-slider-inner .am-pauseplay {
    display: block;
}

.am-slider-inner .am-direction-nav a:before {
    position: relative;
    top: -6px;
}

/*列表*/
.newslist {

}

.newslist li {
    margin-top: 3px;
    margin-bottom: 3px;
    overflow: hidden;
    clear: both;
    display: flex;
    padding-left: 12px;
    position: relative;
}

.newslist li:after {
    content: ' ';
    width: 12px;
    height: 22px;
    position: absolute;
    left: -9px;
    top: 3px;
    background: url(../images/li_ico.png) no-repeat;

}

.newslist li a {
    flex: 1;
    display: block;
    line-height: 28px;
    color: #333;
    font-size: 1.6rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    text-decoration: none;
}

.newslist li a:hover {
    text-decoration: underline;
    color: #DB0000;
}

.newslist li span {
    float: right;
    line-height: 28px;
    color: #666666;
    font-size: 1.6rem;
}

.newslist li:hover span {
    color: #DB0000;
}
/*pc*/
@media only screen and (min-width: 641px) {
    .no-padding-left {
        padding-left: 0px !important;
    }

    .no-padding-right {
        padding-right: 0px !important;
    }
}

/*mobie*/
@media only screen and (max-width: 640px) {
    .nav {
        height: auto !important;
        background: #d93c37;
    }

}